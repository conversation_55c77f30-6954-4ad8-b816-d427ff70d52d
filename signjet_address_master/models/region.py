# -*- coding: utf-8 -*-
# Part of Softprime Consulting Pvt Ltd.
from odoo import models, fields, api


class ResRegion(models.Model):
    _name = 'res.som.region'
    _description = 'Region Details'
    _order = 'id DESC'
    _inherit = "mail.thread"

    name = fields.Char('Region', index=True, tracking=True)
    state_id = fields.Many2one("res.country.state", string='State', domain="[('country_id.phone_code','=',252)]", tracking=True)
    country_id = fields.Many2one('res.country', string='Country', tracking=True)

    @api.onchange('state_id')
    def onchange_state(self):
        """
        If State --> Auto fill Country
        """
        if self.state_id:
            self.country_id = self.state_id.country_id.id
        if not self.state_id:
            self.country_id = []
