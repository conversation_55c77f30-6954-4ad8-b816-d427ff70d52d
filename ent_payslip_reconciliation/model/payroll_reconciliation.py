import base64
from datetime import datetime

import xlsxwriter
from dateutil.relativedelta import relativedelta
from odoo import _, api, fields, models
from odoo.exceptions import UserError

MONTH = [
    ("1", "January"),
    ("2", "February"),
    ("3", "March"),
    ("4", "April"),
    ("5", "May"),
    ("6", "June"),
    ("7", "July"),
    ("8", "August"),
    ("9", "September"),
    ("10", "October"),
    ("11", "November"),
    ("12", "December"),
]

STATE = [
    ("draft", "Draft"),
    ("approval_request", "Approval Request"),
    ("hr_approved", "HR Approved"),
    ("approved", "Approved"),
    ("rejected", "Rejected"),
]


def get_first_day_of_the_month(month, year):
    current_date = datetime.now().date()
    first_date = current_date.replace(day=1, month=month, year=year)
    return first_date


def get_last_day_of_the_month(month, year):
    current_date = datetime.now().date()
    last_date = current_date.replace(day=1, month=month, year=year) + relativedelta(
        months=1, days=-1
    )
    return last_date


class PayrollReconciliation(models.Model):
    _name = "payroll.reconciliation"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Payroll Reconciliation"
    _order = "id DESC"


class PayrollReconciliationLine(models.Model):
    _name = "payroll.reconciliation.line"
    _description = "Payroll Reconciliation Line"
    _order = "id DESC"


class BatchPayrollReconciliation(models.Model):
    _name = "batch.payroll.reconciliation"
    _description = "Batch Payroll Reconciliation"
    _order = "sequence ASC"

    payslip_id = fields.Many2one("hr.payslip", string="Payslip", ondelete="cascade")
    employee_id = fields.Many2one("hr.employee", string="Employee")
    department_id = fields.Many2one("hr.department", "Department")
    job_id = fields.Many2one("hr.job", "Job Tittle")
    sequence = fields.Integer("Sequence")
    type = fields.Selection(
        [("allowance", "Allowance"), ("deduction", "Deduction"), ("basic", "Basic")],
        string="ALW/DED Type",
    )
    slip_id = fields.Many2one("hr.payslip.run", string="Slip", ondelete="cascade")
    name = fields.Char("Type")
    code = fields.Char("Code")
    current_month_amount = fields.Float("Current Month Amount")
    previous_month_amount = fields.Float("Previous Month Amount")
    variance = fields.Float("Variance")
    company_id = fields.Many2one(
        "res.company", "Company", required=True, default=lambda self: self.env.company
    )

    def open_list_details_views(self):
        """Open List Details"""
        details_obj = self.env["show.payslip.detail.batch"]
        details_obj.search([]).unlink()
        previous_end_date = self.slip_id.date_end - relativedelta(months=1)
        previous_start_date = self.slip_id.date_start - relativedelta(months=1)
        details_obj.get_batch_payslip_details(
            from_date=self.slip_id.date_start,
            to_date=self.slip_id.date_end,
            previous_start_date=previous_start_date,
            previous_end_date=previous_end_date,
            company_id=self.slip_id.company_id,
            type=str(self.code),
        )
        action = self.env.ref(
            "ent_payslip_reconciliation.show_payslip_details_batch"
        ).read()[0]
        return action


class HrPayrollRun(models.Model):
    _inherit = "hr.payslip.run"

    month = fields.Selection(MONTH, string="Month")
    batch_payroll_reconciliation_line = fields.One2many(
        "batch.payroll.reconciliation", "slip_id", string="Batch Payroll Reconciliation"
    )

    def get_date_details(self, day, month, year):
        """Get Date Details"""
        if day and month and year:
            date_object = datetime(year, month, day)
            date_only = date_object.date()
            return date_only

    @api.onchange("month")
    def onchange_month_required(self):
        """Onchange Month Required"""
        if self.month:
            current_date = fields.Date.context_today(self)
            current_month = current_date.month
            current_year = current_date.year
            month = int(self.month)
            if month > current_month and current_month != 1:
                message = """Future Month Can not be Selected"""
                self.month = False
                self.date_start = False
                self.date_end = False
                return {"warning": {"title": "Month Error", "message": message}}
            if self.env.company.payroll_cycle == "current_month":
                year = current_year
                self.date_start = get_first_day_of_the_month(month=month, year=year)
                self.date_end = get_last_day_of_the_month(month=month, year=year)
            else:
                if month == 1:
                    year = current_year - 1
                    start_month = 12
                    end_month = month
                    self.date_start = self.get_date_details(
                        day=self.env.company.date_start, month=start_month, year=year
                    )
                    self.date_end = self.get_date_details(
                        day=self.env.company.date_end, month=end_month, year=year + 1
                    )
                else:
                    year = current_year
                    start_month = month - 1
                    end_month = month
                    self.date_start = self.get_date_details(
                        day=self.env.company.date_start, month=start_month, year=year
                    )
                    self.date_end = self.get_date_details(
                        day=self.env.company.date_end, month=end_month, year=year
                    )
        else:
            self.date_start = False
            self.date_end = False

    def open_current_payslip_record(self):
        """open Current Payslip Record"""
        action = self.env.ref("hr_payroll.hr_payslip_action_view_to_pay").read()[0]
        action["domain"] = [("id", "in", self.slip_ids.ids)]
        action["context"] = {"create": False, "edit": False, "view_details": False}
        return action

    def create_payroll_record_report(self):
        """Create payroll Record Report"""
        header_line = ["Reference", "Employee", "Job Title", "Department"]
        for line in self.batch_payroll_reconciliation_line.filtered(
            lambda x: x.current_month_amount != 0.0
        ):
            if line.name != header_line:
                header_line.append(line.name)
        header_line.append("Net Salary")
        f_name = "/tmp/payroll_report_sheet.xlsx"
        workbook = xlsxwriter.Workbook(f_name)
        worksheet = workbook.add_worksheet("Report")
        worksheet.set_column("A:M", 12)
        style = workbook.add_format(
            {"bold": 1, "border": 1, "align": "center", "valign": "vcenter"}
        )
        style.set_font_size(8)
        align_value = workbook.add_format({"align": "center", "valign": "vcenter"})
        align_value.set_font_size(8)
        column = 0
        last_row = 0
        last_col = 4
        for head in header_line:
            row = 0
            worksheet.write(row, column, head, style)
            row += 1
            for slip in self.slip_ids:
                if head == "Reference":
                    worksheet.write(row, column, slip.number, align_value)
                    row += 1
                elif head == "Employee":
                    worksheet.write(row, column, slip.employee_id.name, align_value)
                    row += 1
                elif head == "Job Title":
                    worksheet.write(
                        row, column, slip.job_id and slip.job_id.name or "", align_value
                    )
                    row += 1
                elif head == "Department":
                    worksheet.write(
                        row,
                        column,
                        slip.department_id and slip.department_id.name or "",
                        align_value,
                    )
                    row += 1
                elif head == "Net Salary":
                    worksheet.write(
                        row,
                        column,
                        slip.net_salary or 0.0,
                        align_value,
                    )
                    row += 1
                else:
                    recon_line = slip.batch_payroll_reconciliation_line.filtered(
                        lambda x: x.name == head
                    )
                    amount = (
                        recon_line
                        and sum(recon_line.mapped("current_month_amount"))
                        or 0.0
                    )
                    worksheet.write(row, column, amount, align_value)
                    row += 1
                last_row = row
            column += 1
        worksheet.merge_range(
            "A%s:D%s" % (last_row + 1, last_row + 1), "Total", align_value
        )
        for line_sm in self.batch_payroll_reconciliation_line.filtered(
            lambda x: x.current_month_amount != 0.0
        ):
            sum_amount = line_sm and sum(line_sm.mapped("current_month_amount")) or 0.0
            worksheet.write(last_row, last_col, sum_amount, align_value)
            last_col += 1
        sum_net_salary = sum(self.slip_ids.mapped("total_net_salary"))
        worksheet.write(last_row, last_col, sum_net_salary, align_value)
        workbook.close()
        f = open(f_name, "rb")
        data = f.read()
        f.close()
        name = "Payroll Report"
        out_wizard = self.env["payroll.xlsx.report"].create(
            {"name": name + ".xlsx", "xls_output": base64.encodebytes(data)}
        )
        view_id = self.env.ref(
            "ent_payslip_reconciliation.form_view_payroll_xlsx_report"
        ).id
        return {
            "type": "ir.actions.act_window",
            "name": _(name),
            "res_model": "payroll.xlsx.report",
            "target": "new",
            "view_mode": "form",
            "res_id": out_wizard.id,
            "views": [[view_id, "form"]],
        }

    def update_batch_reconciliation_lines(self):
        if self.batch_payroll_reconciliation_line:
            self.batch_payroll_reconciliation_line.unlink()
        if self.slip_ids:
            self.slip_ids
            previous_end_date = self.date_end - relativedelta(months=1)
            previous_start_date = self.date_start - relativedelta(months=1)
            args = [
                self.date_start,
                self.date_end,
                self.date_start,
                self.date_end,
                previous_start_date,
                previous_end_date,
                previous_start_date,
                previous_end_date,
                self.company_id.id,
            ]
            query_slip = """
                select
                    hpl.name as name,
                    hpl.code as code,
                    hpl.sequence as sequence,
                    cat.code as category_code,
                    sum(CASE
                        WHEN (hp.date_from BETWEEN  %s and  %s
                        or hp.date_to BETWEEN  %s and %s)
                        THEN hpl.amount * hpl.quantity else 0
                        END) as total,
                    sum(CASE
                        WHEN (hp.date_from BETWEEN  %s and  %s
                        or hp.date_to BETWEEN  %s and %s)
                        THEN hpl.amount * hpl.quantity else 0
                        END) as
                        last_total
                from hr_payslip_line hpl
                left join hr_payslip hp on hp.id = hpl.slip_id
                left join hr_salary_rule sr on sr.id = hpl.salary_rule_id
                left join hr_salary_rule_category cat on cat.id = sr.category_id
                where
                    hpl.code not in ('GROSS', 'NET')
                    and company_id = %s
                    and hp.state != 'cancel'
                group by
                    hpl.name,
                    hpl.code,
                    hpl.sequence,
                    cat.code
                order by
                    hpl.sequence DESC
            """
            self.env.cr.execute(query_slip, tuple(args))
            result_slip = self.env.cr.fetchall()
            list_line = []
            seq_alw = 2
            seq_ded = 100
            for line in result_slip:
                if (line[4] != 0.0 or line[5] != 0.0) and line[3] != None:
                    type = "basic"
                    if line[1] != "BASIC" and line[3] == "DED":
                        type = "deduction"
                    if line[1] != "BASIC" and line[3] == "ALW":
                        type = "allowance"
                    if type == "basic":
                        vals = {
                            "name": line[0],
                            "code": line[1],
                            "sequence": line[2],
                            "type": type,
                            "current_month_amount": line[4],
                            "previous_month_amount": line[5],
                            "slip_id": self.id,
                            "variance": line[4] - line[5],
                        }
                        list_line.append(vals)
                    if type == "allowance":
                        vals = {
                            "name": line[0],
                            "code": line[1],
                            "sequence": line[2],
                            "type": type,
                            "current_month_amount": line[4],
                            "previous_month_amount": line[5],
                            "slip_id": self.id,
                            "variance": line[4] - line[5],
                        }
                        list_line.append(vals)
                        seq_alw += 1
                    if type == "deduction":
                        vals = {
                            "name": line[0],
                            "code": line[1],
                            "sequence": line[2],
                            "type": type,
                            "current_month_amount": -1 * line[4],
                            "previous_month_amount": -1 * line[5],
                            "slip_id": self.id,
                            "variance": -1 * (line[4] - line[5]),
                        }
                        list_line.append(vals)
                        seq_ded += 1
            if list_line:
                self.env["batch.payroll.reconciliation"].create(list_line)


class HrPayslip(models.Model):
    _inherit = "hr.payslip"

    batch_payroll_reconciliation_line = fields.One2many(
        "batch.payroll.reconciliation",
        "payslip_id",
        string="Batch Payroll Reconciliation",
    )
    department_id = fields.Many2one(
        "hr.department", "Department", related="employee_id.department_id", store=True
    )
    job_id = fields.Many2one(
        "hr.job", "Job Tittle", related="employee_id.job_id", store=True
    )
    total_basic_salary = fields.Float(
        "Basic", compute="get_salary_details_total_sum", store=True
    )
    total_main_allowance = fields.Float(
        "Allowance", compute="get_salary_details_total_sum", store=True
    )
    total_main_deduction = fields.Float(
        "Deduction", compute="get_salary_details_total_sum", store=True
    )
    total_net_salary = fields.Float(
        "Net", compute="get_salary_details_total_sum", store=True
    )

    def unlink(self):
        if any(self.filtered(lambda payslip: payslip.state not in ("draft", "cancel"))):
            raise UserError(
                _("You cannot delete a payslip which is not draft or cancelled!")
            )
        slip_run_id = self.payslip_run_id
        res = super().unlink()
        slip_run_id.update_batch_reconciliation_lines()
        return res

    def open_list_details_views(self):
        """Open List Details"""
        tree_view_id = self.env.ref(
            "ent_payslip_reconciliation.batch_payroll_reconciliation_form_view"
        )
        domain = [("payslip_id", "=", self.id), ("type", "!=", "basic")]
        action = {
            "name": "Employee",
            "type": "ir.actions.act_window",
            "context": "{'search_default_type_group_by': 1}",
            "res_model": "batch.payroll.reconciliation",
            "target": "current",
            "domain": domain,
            "view_mode": "tree",
            "view_ids": [
                (5, 0, 0),
                (0, 0, {"view_mode": "form", "view_id": tree_view_id}),
            ],
        }
        return action

    @api.depends("line_ids.amount", "line_ids.quantity")
    def get_salary_details_total_sum(self):
        """Method to check salary details on payslip line and update on payslip"""
        for salary in self:
            if salary.line_ids:
                basic_salary = salary.line_ids.filtered(lambda x: x.code == "BASIC")
                salary.total_basic_salary = basic_salary.amount * basic_salary.quantity
                allowance = salary.line_ids.filtered(
                    lambda x: x.salary_rule_id.category_id.code == "ALW"
                )
                salary.total_main_allowance = sum(
                    line.amount * line.quantity for line in allowance
                )
                deduction = salary.line_ids.filtered(
                    lambda x: x.salary_rule_id.category_id.code == "DED"
                )
                salary.total_main_deduction = sum(
                    line.amount * line.quantity for line in deduction
                )
                new_salary = salary.line_ids.filtered(lambda x: x.code == "NET")
                salary.total_net_salary = new_salary.amount * new_salary.quantity

    def update_batch_reconciliation_lines(self):
        """Update Batch Payslip Employee"""
        if self.batch_payroll_reconciliation_line:
            self.batch_payroll_reconciliation_line.unlink()
        previous_payslip_query = """
                            select
                                id
                            from hr_payslip
                            where
                                employee_id = %s
                                and state in ('done', 'paid', 'partially_paid')
                                and id != %s
                            order by id DESC
                            limit 1
                            """ % (
            self.employee_id.id,
            self.id,
        )
        self.env.cr.execute(previous_payslip_query)
        pre_payslip = self.env.cr.fetchone()
        pre_payslip_result = pre_payslip and pre_payslip[0] or False
        pre_run_id = self.env["hr.payslip"].browse(pre_payslip_result)
        args = [self.id, self.employee_id.id]
        query_slip = """
                    with table_current as(
                        select
                            hpl.name as name,
                            hpl.code as code,
                            hpl.sequence as sequence,
                            sum(hpl.amount * hpl.quantity) as total,
                            0.0 as last_total,
                            cat.code as category_code
                        from hr_payslip_line hpl
                        left join hr_payslip hp on hp.id = hpl.slip_id
                        left join hr_salary_rule sr on sr.id = hpl.salary_rule_id
                        left join hr_salary_rule_category cat on cat.id = sr.category_id
                        where
                            hpl.slip_id = %s
                            and hp.employee_id = %s
                            and hpl.code not in ('GROSS', 'NET')
                        group by
                            hpl.name,
                            hpl.code,
                            hpl.sequence,
                            cat.code
                        order by
                            hpl.sequence DESC
                            ),
                    data as(
                    select * from table_current
                    )
                    select
                        name,
                        code,
                        sequence,
                        COALESCE(sum(total), 0.0),
                        COALESCE(sum(last_total), 0.0),
                        category_code
                    from data
                    group by
                        name,
                        code,
                        sequence,
                        category_code
                    order by
                        sequence DESC
                    """
        if pre_payslip_result:
            args = [
                self.id,
                self.employee_id.id,
                pre_payslip_result,
                pre_run_id.employee_id.id,
            ]
            query_slip = """
                        with table_current as(
                            select
                                hpl.name as name,
                                hpl.code as code,
                                hpl.sequence as sequence,
                                sum(hpl.amount * hpl.quantity) as total,
                                0.0 as last_total,
                                cat.code as category_code
                            from hr_payslip_line hpl
                            left join hr_payslip hp on hp.id = hpl.slip_id
                            left join hr_salary_rule sr on sr.id = hpl.salary_rule_id
                            left join hr_salary_rule_category cat on cat.id = sr.category_id
                            where
                                hpl.slip_id = %s
                                and hp.employee_id = %s
                                and hpl.code not in ('GROSS', 'NET')
                            group by
                                hpl.name,
                                hpl.code,
                                hpl.sequence,
                                cat.code
                            order by
                                hpl.sequence DESC
                            ),
                        table_last as(
                            select
                                hpl.name as name,
                                hpl.code as code,
                                hpl.sequence as sequence,
                                0.0 as total,
                                sum(hpl.amount * hpl.quantity) as last_total,
                                cat.code as category_code
                            from hr_payslip_line hpl
                            left join hr_payslip hp on hp.id = hpl.slip_id
                            left join hr_salary_rule sr on sr.id = hpl.salary_rule_id
                            left join hr_salary_rule_category cat on cat.id = sr.category_id

                            where
                                hpl.slip_id = %s
                                and hp.employee_id = %s
                                and hpl.code not in ('GROSS', 'NET')
                            group by
                                hpl.name,
                                hpl.code,
                                hpl.sequence,
                                cat.code
                            order by
                                hpl.sequence DESC
                            ),
                        data as(
                        select * from table_current
                        union
                        select * from table_last
                        )
                        select
                            name,
                            code,
                            sequence,
                            COALESCE(sum(total), 0.0),
                            COALESCE(sum(last_total), 0.0),
                            category_code
                        from data
                        group by
                            name,
                            code,
                            sequence,
                            category_code
                        order by
                            sequence DESC
                        """
        self.env.cr.execute(query_slip, tuple(args))
        result_slip = self.env.cr.fetchall()
        list_line = []
        seq_alw = 2
        seq_ded = 100
        for line in result_slip:
            if line[3] != 0.0 or line[4] != 0.0:
                type = "basic"
                if line[1] != "BASIC" and line[5] == "DED":
                    type = "deduction"
                if line[1] != "BASIC" and line[5] == "ALW":
                    type = "allowance"
                if type == "basic":
                    vals = {
                        "name": line[0],
                        "code": line[1],
                        "sequence": line[2],
                        "type": type,
                        "current_month_amount": line[3],
                        "previous_month_amount": line[4],
                        "payslip_id": self.id,
                        "employee_id": self.employee_id.id,
                        "department_id": self.department_id.id,
                        "job_id": self.job_id.id,
                        "variance": line[3] - line[4],
                    }
                    list_line.append(vals)
                if type == "allowance":
                    vals = {
                        "name": line[0],
                        "code": line[1],
                        "sequence": line[2],
                        "type": type,
                        "current_month_amount": line[3],
                        "previous_month_amount": line[4],
                        "payslip_id": self.id,
                        "employee_id": self.employee_id.id,
                        "department_id": self.department_id.id,
                        "job_id": self.job_id.id,
                        "variance": line[3] - line[4],
                    }
                    list_line.append(vals)
                    seq_alw += 1
                if type == "deduction":
                    vals = {
                        "name": line[0],
                        "code": line[1],
                        "sequence": line[2],
                        "type": type,
                        "current_month_amount": -1 * line[3],
                        "previous_month_amount": -1 * line[4],
                        "payslip_id": self.id,
                        "employee_id": self.employee_id.id,
                        "department_id": self.department_id.id,
                        "job_id": self.job_id.id,
                        "variance": -1 * (line[3] - line[4]),
                    }
                    list_line.append(vals)
                    seq_ded += 1
        if list_line:
            self.env["batch.payroll.reconciliation"].create(list_line)


class HrPayslipEmployees(models.TransientModel):
    _inherit = "hr.payslip.employees"

    def compute_sheet(self):
        """
        inherit for add department in batch payslip
        """
        res = super().compute_sheet()
        active = self.env.context.get("active_id")
        batch_id = self.env["hr.payslip.run"].browse(active)
        if batch_id:
            for rec in batch_id.slip_ids:
                rec.update_batch_reconciliation_lines()
            batch_id.update_batch_reconciliation_lines()
        return res
