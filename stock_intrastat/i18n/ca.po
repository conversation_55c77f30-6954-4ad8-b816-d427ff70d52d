# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_intrastat
# 
# Translators:
# <PERSON>, 2021
# j<PERSON><PERSON><PERSON>, 2021
# ericrolo, 2022
# Jonatan Gk, 2022
# ma<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:43+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_intrastat
#: model:ir.model.fields,field_description:stock_intrastat.field_stock_warehouse__company_country_id
msgid "Fiscal Country"
msgstr "País Fiscal"

#. module: stock_intrastat
#: model:ir.model,name:stock_intrastat.model_account_intrastat_report
msgid "Intrastat Report"
msgstr "Informe Intrastat"

#. module: stock_intrastat
#: model:ir.model.fields,field_description:stock_intrastat.field_stock_warehouse__intrastat_region_id
msgid "Intrastat region"
msgstr "Regió d'Intrastat"

#. module: stock_intrastat
#: model:ir.model.fields,help:stock_intrastat.field_stock_warehouse__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr "El país ha d'utilitzar els informes d'impostos d'aquesta empresa."

#. module: stock_intrastat
#: model:ir.model,name:stock_intrastat.model_stock_warehouse
msgid "Warehouse"
msgstr "Magatzem"
