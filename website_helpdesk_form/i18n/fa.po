# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_form
# 
# Translators:
# <PERSON> <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:44+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submited
#: model_terms:website.page,arch_db:website_helpdesk_form.aboutus_page
msgid ""
"<i class=\"fa fa-check-circle fa-1x text-success mr-2\" role=\"img\" aria-"
"label=\"Success\" title=\"Success\"/>"
msgstr ""

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid "<span class=\"s_website_form_label_content\">Attachment(s)</span>"
msgstr ""

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid "<span class=\"s_website_form_label_content\">Description</span>"
msgstr ""

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid "<span class=\"s_website_form_label_content\">Team</span>"
msgstr ""

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_helpdesk_form
#. openerp-web
#: code:addons/website_helpdesk_form/static/src/js/website_helpdesk_form_editor.js:0
#, python-format
msgid "Description"
msgstr "توصیف"

#. module: website_helpdesk_form
#: model:ir.model.fields,field_description:website_helpdesk_form.field_helpdesk_team__website_form_view_id
msgid "Form"
msgstr "فرم"

#. module: website_helpdesk_form
#. openerp-web
#: code:addons/website_helpdesk_form/static/src/js/website_helpdesk_form_editor.js:0
#: model:ir.model,name:website_helpdesk_form.model_helpdesk_team
#, python-format
msgid "Helpdesk Team"
msgstr "تیم میز پشتیبانی"

#. module: website_helpdesk_form
#. openerp-web
#: code:addons/website_helpdesk_form/static/src/js/website_helpdesk_form_editor.js:0
#, python-format
msgid "Subject"
msgstr "موضوع"

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid "Submit"
msgstr "تقدیم"

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submit_form
msgid "Submit a Ticket"
msgstr ""

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submited
#: model_terms:website.page,arch_db:website_helpdesk_form.aboutus_page
msgid "Thank you for submitting your issue, our team will get right on it!"
msgstr "با تشکر از گزارش مشکل، تیم ما مستقیما به آن خواهند پرداخت!"

#. module: website_helpdesk_form
#. openerp-web
#: code:addons/website_helpdesk_form/static/src/js/website_helpdesk_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "ایمیل شما"

#. module: website_helpdesk_form
#. openerp-web
#: code:addons/website_helpdesk_form/static/src/js/website_helpdesk_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "نام"

#. module: website_helpdesk_form
#: model_terms:ir.ui.view,arch_db:website_helpdesk_form.ticket_submited
#: model_terms:website.page,arch_db:website_helpdesk_form.aboutus_page
msgid "Your Ticket Number is"
msgstr "شماره تیکت شما عبارت است از"
