# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from collections import Counter
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from odoo import Command, _, api, fields, models
from odoo.exceptions import UserError


class RentingContract(models.Model):
    _name = "billboard.renting.contract"
    _inherit = "mail.thread"
    _description = "Renting Contract of Billboard"
    _order = "id desc, create_date desc"

    def _domain(self):
        asset = self._billboard()
        if len(asset) > 0:
            return [
                ("id", "in", asset),
                ("company_id", "=", self.company_id.id),
                ("is_billboard", "=", True),
            ]
        return [("company_id", "in", self.company_id.id), ("is_billboard", "=", True)]

    name = fields.Char()
    serial_no = fields.Char(
        required=True,
        readonly=True,
        default=lambda self: _("New"),
        string="S/N"
    )
    contract_stage = fields.Selection(
        [
            ("draft", "Draft"),
            ("booked", "Booked"),
            ("submitted_customer_approval", "Submitted For Customer Approval"),
            ("customer_signed", "Customer Signed"),
            ("running", "Running"),
            ("on_hold", "On Hold"),
            ("cancelled", "Cancelled"),
            ("terminated", "Terminated"),
        ],
        tracking=True,
        default="draft",
    )
    previous_stage = fields.Char(default="draft")
    partner_id = fields.Many2one(
        "res.partner", index=True, tracking=1, string="Customer"
    )
    vendor_id = fields.Many2one(
        "res.partner", index=True, tracking=1, string="Outsource Vendor"
    )
    user_id = fields.Many2one(
        "res.users", tracking=1, default=lambda self: self.env.user.id
    )
    company_id = fields.Many2one(
        "res.company",
        index=True,
        tracking=1,
        default=lambda self: self.env.company,
    )
    billboard_asset_id = fields.Many2one("account.asset")
    billboard_asset_remark = fields.Char()
    billboard_asset_ids = fields.Many2many("account.asset", "billboard_asset_rel")
    billboard_discount = fields.Float("Billboard Discount", tracking=True)
    billboard_asset_line = fields.One2many(
        "billboard.asset.rc.line", "rc_id", copy=False, tracking=True
    )
    billing_frequency = fields.Selection(
        [
            ("daily", "Daily"),
            ("monthly", "Monthly"),
            ("quarterly", "Quarterly"),
            ("semiannually", "SemiAnnually"),
            ("yearly", "Yearly"),
        ],
        tracking=True,
    )
    start_date = fields.Date(tracking=True)
    end_date = fields.Date(tracking=True)
    picture_ids = fields.Many2many("ir.attachment", tracking=True)
    rc_signed_ids = fields.Many2many("ir.attachment", "rc_signed_rel", tracking=True)
    prev_invoice_generated = fields.Date()
    branch_id = fields.Many2one("res.branch")
    is_booked = fields.Boolean()  # Is billboard asset booked
    is_line_discount = fields.Boolean()
    is_global_discount = fields.Boolean()
    history_line = fields.One2many("billboard.rc.history", "rc_id", tracking=True)
    renewed_from_rc_id = fields.Many2one("billboard.renting.contract")
    renewed_ids = fields.Many2many(
        "billboard.renting.contract", "renewed_rc_rel", "rc_id", "renewed_id"
    )
    renew_contract_count = fields.Integer()
    renew_sequence = fields.Integer(default=1)
    total_discount = fields.Float('Total Discount', compute='_total_amount', store=True)
    total_rental_price = fields.Float('Total Rental Amount', compute='_total_amount', store=True)
    total_amount = fields.Float('Total Amount', compute='_total_amount', store=True)
    renting_total_amount = fields.Float(
        'Renting Total Amount',
        compute='_renting_total_amount',
        store=True
    )
    sale_id = fields.Many2one("sale.order")
    sale_line_id = fields.Many2one("sale.order.line")
    advance_amount = fields.Float('Advance Amount')
    accounting_entry_ids = fields.Many2many(
        'account.move',
        'accounting_entry_ids_account_move_rel'
    )
    is_advance_amount_exceed = fields.Boolean(
        string='Is Advance Amount Exceed',
        compute='_compute_total_paid_amt',
        store=True
    )
    remaining_amt = fields.Float(string='Remaining Amount',
                                 compute='_compute_total_paid_amt',
                                 store=True)
    invoice_paid_amt = fields.Float('Invoice Paid Reconcile Amount')
    invoice_payment_ids = fields.Many2many(
        'account.payment',
        'invoice_payment_ids_account_payment_rel',
        string='Invoice Payment'
    )
    is_printing_sale_billboard = fields.Boolean(default=False, copy=False)
    renting_invoice_ids = fields.Many2many("account.move", "renting_account_move_rel", string="Renting Invoices")
    manual_advance_payment_entry_ids = fields.Many2many(
        'account.move',
        'manual_advance_payment_entry_ids_account_move_rel',
        string='Advance Payment Entry'
    )
    payment_method_id = fields.Many2one('account.journal', string='Payment Method', copy=False)
    renting_notes = fields.Html(
        string="Billboard Contract Template",
        compute="compute_billboard_renting_notes"
    )
    billboard_attachment_url = fields.Char(string="Signed Contract Attachment URL")
    is_billboard_outsource = fields.Boolean(default=False)
    outsource_po_ids = fields.Many2many(
        "purchase.order",
        "outsource_billboard_po_rel",
        string="Outsource PO's"
    )
    billboard_contract_payment_term_line = fields.One2many(
        "billboard.contract.payment.term.line",
        "renting_contract_id",
        string="Payment Term Line"
    )

    @api.depends('advance_amount', 'total_amount')
    def _compute_total_paid_amt(self):
        for amt in self:
            amt.is_advance_amount_exceed = False

            amt.remaining_amt = amt.total_amount - amt.advance_amount
            if amt.advance_amount > amt.total_amount:
                amt.remaining_amt = 0.0
                amt.is_advance_amount_exceed = True

    @api.depends("total_discount", "total_rental_price")
    def _total_amount(self):
        for amt in self:
            amt.total_discount = 0.0
            amt.total_amount = 0.0
            amt.total_rental_price = 0.0
            total_discount = sum(amt.billboard_asset_line.mapped("discount"))
            total_rental_price = sum(amt.billboard_asset_line.mapped("rental_price"))
            amt.total_discount = total_discount
            amt.total_rental_price = total_rental_price
            amt.total_amount = total_rental_price - total_discount

    @api.depends('total_discount', 'total_rental_price')
    def _renting_total_amount(self):
        for amt in self:
            amt.renting_total_amount = 0.0
            total_discount = sum(amt.billboard_asset_line.mapped('discount'))
            total_rental_price = sum(amt.billboard_asset_line.mapped('rental_price'))
            amt.renting_total_amount = total_rental_price - total_discount

    def compute_billboard_renting_notes(self):
        for rec in self:
            rec.renting_notes = False
            master_renting_notes = self.env["billboard.template.master"].sudo().search(
                [
                    ("renting_notes", "!=", ""),
                    ("status", "=", "confirmed"),
                ]
            )
            if master_renting_notes:
                rec.renting_notes = master_renting_notes[0].renting_notes

    def action_available(self):
        """
        Method to make available
        :return:
        """
        if self.contract_stage == "booked":
            self.contract_stage = "draft"
            self.is_booked = False
            for line in self.billboard_asset_line:
                line.stage = "available"
                self.billboard_asset_ids = [(4, line.billboard_asset_id.id)]
                line.billboard_asset_id.rc_ids = [(3, line.rc_id.id)]
                for rc_line in line.billboard_asset_id.rc_line:
                    if (
                            rc_line.rc_id.id == self.id
                            and rc_line.contract_stage == "booked"
                    ):
                        rc_line.contract_stage = "terminated"
                        rc_line.asset_id.stage = "available"
                    if rc_line.contract_stage != "booked":
                        rc_line.asset_id.stage = rc_line.asset_id.stage
                    else:
                        rc_line.asset_id.stage = "available"

    @api.model
    def create(self, vals):
        """Serial Numer generate"""
        if "billboard_asset_line" in vals:
            if not vals["billboard_asset_line"]:
                raise UserError(_("Please add Billboard Assets to continue."))
            # if vals['billboard_asset_line']:
        if vals.get("serial_no", _("New")) == _("New"):
            vals["serial_no"] = self.env["ir.sequence"].next_by_code(
                "billboard.renting.contract.seq"
            ) or _("New")
        # self.validate_renewed_contract()
        res = super().create(vals)
        # res.validate_renew_date(res=res)
        return res

    def validate_renew_date(self, res):
        """
        Method to validate renew date
        :return:
        """
        for line in res.billboard_asset_line:
            renting_contract_ids = self.env["billboard.asset.rc.line"].search(
                [
                    ("billboard_asset_id", "=", line.billboard_asset_id.id),
                    ("billboard_type_id", "=", line.billboard_type_id.id),
                    ("rc_id.start_date", ">=", res.start_date),
                    ("rc_id.end_date", "<=", res.end_date),
                    ("stage", "in", ["booked", "rented", "damaged"]),
                    ("rc_id", "!=", res.id),
                ]
            )
            if renting_contract_ids:
                raise UserError(
                    _(
                        f"Billboard Asset is not available from {res.start_date} to"
                        f" {res.end_date} for {res.name}"
                    )
                )

    def prepare_outsource_po_line(self):
        po_list = []
        if self.sale_id and self.billboard_asset_line:
            for line in self.billboard_asset_line:
                if line.is_billboard_outsource:
                    po_line = Command.create({
                        'name': line.sale_line_id.name,
                        'product_qty': line.sale_line_id.product_uom_qty,
                        'price_unit': line.sale_line_id.price_unit,
                        'sale_id': self.sale_id.id,
                        'sale_line_id': line.sale_line_id.id,
                        'renting_contract_id': self.id,
                        'renting_contract_line_id': line.id,
                        'is_billboard_outsource': True,
                        'product_id': line.sale_line_id.product_id.id,
                    })
                    po_list.append(po_line)
        return po_list

    def action_billboard_outsource(self):
        if any(line.is_billboard_outsource for line in self.billboard_asset_line):
            po_vals = {
                'partner_id': self.vendor_id.id,
                'branch_id': self.branch_id.id if self.branch_id else False,
                'sale_id': self.sale_id.id,
                'is_billboard_outsource': True,
                'renting_contract_id': self.id,
                'date_order': self.start_date,
                'company_id': self.company_id.id,
                'order_line': self.prepare_outsource_po_line(),
            }
            purchase_id = self.env['purchase.order'].create(po_vals)
            self.outsource_po_ids |= purchase_id

    def action_view_contract_purchase_orders(self):
        if self.outsource_po_ids:
            action = (self.env.ref(
                "purchase.purchase_rfq"
            ).sudo().read()[0]
            )
            action["domain"] = [
                ("id", "=", self.outsource_po_ids.ids),
            ]
            return action

    def validate_renewed_contract(self, res):
        """Validate Renewed Renting Contract"""
        name = []
        if self.billboard_asset_line:
            self.onchange_date()
            for asset in self.billboard_asset_line:
                if asset.billboard_asset_id.id in self.billboard_asset_ids.ids:
                    name.append(asset.billboard_asset_id.name)
        if name:
            raise UserError(
                _(
                    f"Billboard Asset/s {name} are not available from {res.start_date} to"
                    f" {res.end_date} for {res.name}"
                )
            )

    @api.constrains("start_date", "end_date")
    def validate_date(self):
        """Date cannot be past date and end date cannot be less than start date"""
        if self.start_date:
            if self.start_date < fields.Date.context_today(self):
                raise UserError(_("Start date cannot be past date!"))
        if self.end_date:
            if self.end_date < fields.Date.context_today(self):
                raise UserError(_("End date cannot be past date!"))
        if self.start_date and self.end_date:
            if self.end_date < self.start_date:
                raise UserError(_("End date cannot less than start date!"))

    def calculate_net_price(self):
        """Calculate Net Price of Billboard Asset Line"""
        for line in self.billboard_asset_line:
            line.net_price = line.rental_price
            if line.net_price < 0:
                self.billboard_discount = 0
                self.billboard_asset_line.discount = 0
                raise UserError(
                    _(
                        f"Net Price Cannot be Negative for {line.billboard_asset_id.name}. "
                        f"Please provide a valid Discount Amount!"
                    )
                )

    @api.onchange(
        "billboard_asset_line",
        "billboard_asset_line.discount",
        "billboard_asset_line.billboard_asset_id",
    )
    def onchange_billboard_asset(self):
        """Billboard asset"""
        # 1)
        if self.billboard_asset_line and not self.start_date and not self.end_date:
            raise UserError(
                _("To select billboard asset you have to provide start and end date.")
            )
        # 2) Discount
        if self.billboard_discount:
            if any(line.is_line_discount for line in self.billboard_asset_line):
                discount = [line.discount for line in self.billboard_asset_line]
                self.billboard_discount = sum(discount)
            else:
                if self.billboard_asset_line:
                    self.billboard_asset_line.discount = self.billboard_discount / len(
                        self.billboard_asset_line.billboard_asset_id
                    )
                    self.calculate_net_price()
        # 3) Duplicate asset in line
        if self.billboard_asset_line.billboard_asset_id:
            asset_ids = self.billboard_asset_line.billboard_asset_id.ids
            count_assets = Counter(asset_ids)
            for asset in count_assets:
                if count_assets[asset] > 1:
                    raise UserError(
                        _(f"The Billboard Asset {asset.name} is already applied.")
                    )

    @api.onchange("billboard_discount")
    def onchange_billboard_discount(self):
        """Onchange Billboard Discount"""
        if self.billboard_asset_line and self.billboard_asset_line.billboard_asset_id:
            if all(not line.is_line_discount for line in self.billboard_asset_line):
                self.billboard_asset_line.discount = self.billboard_discount / len(
                    self.billboard_asset_line.billboard_asset_id
                )
                self.calculate_net_price()
            else:
                self.billboard_asset_line.is_line_discount = False

    def compute_billing_dates(self):
        self.ensure_one()
        billing_dates = []
        current_date = self.start_date
        while current_date <= self.end_date:
            billing_dates.append(current_date)
            if self.billing_frequency == 'daily':
                current_date += timedelta(days=1)
            elif self.billing_frequency == 'monthly':
                current_date += relativedelta(months=1)
            elif self.billing_frequency == 'quarterly':
                current_date += relativedelta(months=3)
            elif self.billing_frequency == 'semiannually':
                current_date += relativedelta(months=6)
            elif self.billing_frequency == 'yearly':
                current_date += relativedelta(years=1)
        return billing_dates

    def calculate_date_ranges(self):
        date_ranges = []
        current_date = self.start_date
        while current_date < self.end_date:
            if self.billing_frequency == 'daily':
                next_date = current_date + timedelta(days=1)
            elif self.billing_frequency == 'monthly':
                next_date = current_date + relativedelta(months=1)
            elif self.billing_frequency == 'quarterly':
                next_date = current_date + relativedelta(months=3)
            elif self.billing_frequency == 'semiannually':
                next_date = current_date + relativedelta(months=6)
            elif self.billing_frequency == 'yearly':
                next_date = current_date + relativedelta(years=1)
            else:
                break
            end_range = min(next_date, self.end_date)
            date_ranges.append((current_date, end_range))
            current_date = next_date
        return date_ranges

    def action_print_renting_contract_report(self):
        if self.partner_id:
            return self.env.ref("signjet_billboard.action_renting_contract_report").report_action(self)

    @api.onchange("billing_frequency")
    def onchange_billing_frequency(self):
        """Billing Frequency based on start and end date"""
        if self.billing_frequency and self.start_date and self.end_date:
            self.validate_billing_frequency()

    def validate_billing_frequency(self):
        """Checking Billing Frequency"""
        diff = (self.end_date - self.start_date).days
        if self.billing_frequency == "daily":
            if diff < 1:
                raise UserError(
                    _(
                        "The difference between end_date and start_date should be "
                        "at least 1 day because the billing frequency is selected on daily basis."
                    )
                )
        elif self.billing_frequency == "monthly":
            if diff < 30:
                raise UserError(
                    _(
                        "The difference between end_date and start_date should be at "
                        "least 30 days (1 month) because the billing frequency "
                        "is selected on monthly basis."
                    )
                )
        elif self.billing_frequency == "quarterly":
            if diff < 90:
                raise UserError(
                    _(
                        "The difference between end_date and start_date "
                        "should be at least 90 days (3 months) because "
                        "the billing frequency is selected on quarterly basis."
                    )
                )
        elif self.billing_frequency == "semiannually":
            if diff < 180:
                raise UserError(
                    _(
                        "The difference between end_date and start_date should be "
                        "at least 180 days (6 months) because the billing "
                        "frequency is selected on semiannually basis."
                    )
                )
        elif self.billing_frequency == "yearly":
            if diff < 365:
                raise UserError(
                    _(
                        "The difference between end_date and start_date should be "
                        "at least 365 days (1 year) because the billing frequency "
                        "is selected on monthly basis."
                    )
                )

    def action_get_available_billboard(self):
        """
        get available billboard for select
        """
        self.update_available_billboard_asset()

    @api.onchange("start_date", "end_date")
    def onchange_date(self):
        """Date cannot be past date and end date cannot be less than start date"""
        if self.start_date and self.end_date:
            self.update_available_billboard_asset()

    def prepare_contract_reminder_vals(self):
        """Check Billboard asset availability"""
        contract_renew = []
        if self.end_date == (
                fields.Date.context_today(self) + timedelta(days=self.company_id.contract_renewal_reminder_days)
        ):
            contract_renew.append(self.name)
        return contract_renew

    def action_send_billboard_renewal_reminder(self):
        if self.contract_stage == "running":
            contract_renew = self.prepare_contract_reminder_vals()
            if contract_renew:
                template_id = self.env.ref('signjet_billboard.email_template_billboard_contract_renewal').id
                template = self.env['mail.template'].browse(template_id)
                for record in self:
                    if record.partner_id:
                        template.send_mail(record.id, force_send=True, email_values={
                            'email_to': record.partner_id.email,
                        })
                self.write(
                    {'contract_stage': 'running'})
                return True

    def update_available_billboard_asset(self):
        """Check Billboard asset availability"""
        name = []
        billboard_asset_ids = self.env["account.asset"].search(
            [
                ("is_billboard", "=", True),
                ("company_id", "=", self.company_id.id),
                ("stage", "!=", "damaged"),
            ]
        )
        billboard = []
        if billboard_asset_ids:
            for billboard_asset_id in billboard_asset_ids:
                if billboard_asset_id.rc_ids:
                    for rc_asset in billboard_asset_id.rc_ids:
                        if (
                                rc_asset.start_date <= self.start_date <= rc_asset.end_date
                                or rc_asset.start_date <= self.end_date <= rc_asset.end_date
                        ):
                            if rc_asset.contract_stage in ("terminated", "cancelled"):
                                billboard.append(billboard_asset_id.id)
                                name.append(billboard_asset_id.name)
                            else:
                                if billboard_asset_id.id in billboard:
                                    billboard.remove(billboard_asset_id.id)
                                break
                        else:
                            billboard.append(billboard_asset_id.id)
                            name.append(billboard_asset_id.name)
                else:
                    billboard.append(billboard_asset_id.id)
                    name.append(billboard_asset_id.name)
        self.billboard_asset_ids = [(6, 0, billboard)]

    def _get_billboard_address(self, billboard):
        """Billboard address"""
        address = ""
        if billboard.billboard_asset_id:
            billboard_address = billboard.billboard_asset_id
            if billboard_address.street_id:
                address += billboard_address.street_id.name + ", "
            if billboard_address.city_id:
                address += billboard_address.city_id.name + ", "
            if billboard_address.region_id:
                address += billboard_address.region_id.name + ", "
            if billboard_address.district_id.name:
                address += billboard_address.district_id.name + ", "
            if billboard_address.state_id:
                address += billboard_address.state_id.name + ", "
            if billboard_address.country_id:
                address += billboard_address.country_id.name
        return address

    def create_history(self, history_type, remark):
        """Create Billboard Asset History"""
        history = {
            "user_id": self.env.user.id,
            "date": fields.Datetime.now(),
            "type": history_type,
            "remark": remark,
            "rc_id": self.id,
        }
        self.env["billboard.rc.history"].create(history)

    # ================================ Invoice ================================

    def cron_update_billboard_rc_line(self, billboard, billboard_rc_id):
        for rc in billboard.rc_line:
            if rc.rc_id.id == billboard_rc_id.id:
                rc.contract_stage = billboard_rc_id.contract_stage
            if rc.contract_stage != "terminated":
                rc.asset_id.sudo().stage = "booked"
            else:
                rc.asset_id.sudo().stage = "available"

    def cron_update_billboard_asset(self, asset, billboard_rc_id):
        self.cron_update_billboard_rc_line(
            billboard=asset.billboard_asset_id, billboard_rc_id=billboard_rc_id
        )
        for rc in asset.billboard_asset_id.rc_ids:
            if rc.contract_stage == "booked":
                asset.billboard_asset_id.sudo().stage = "booked"
                asset.sudo().stage = "booked"
            if rc.contract_stage == "rented":
                asset.billboard_asset_id.sudo().stage = "rented"
                asset.sudo().stage = "rented"
                break

    def terminate_renting_contract(self):
        """Terminate Renting Contract If End Date of Contract is of today"""
        rc_ids = self.env["billboard.renting.contract"].search(
            [("contract_stage", "=", "running")]
        )
        for rc_id in rc_ids:
            if rc_id.end_date < fields.Date.context_today(self):
                rc_id.contract_stage = "terminated"
                for asset in rc_id.billboard_asset_line:
                    asset.stage = "available"
                    self.cron_update_billboard_asset(asset=asset, billboard_rc_id=rc_id)
                    asset.sudo().stage = "available"

    def generate_scheduled_multiple_invoice(self, renting_contract, line):
        """Multiple Invoices for Scheduler"""
        curr_invoice_line = []
        if renting_contract and line:
            if not line.is_onhold:
                invoice_line_ids = renting_contract.prepare_scheduler_invoice_line(
                    billboard=line, renting_contract=renting_contract
                )
                curr_invoice_line.append((0, 0, invoice_line_ids))
            invoice_id = self.env["account.move"].create(
                renting_contract.prepare_scheduler_invoice(
                    rc=renting_contract,
                    line=line,
                    invoice_line_ids=curr_invoice_line,
                    invoice_date=fields.Date.context_today(self),
                )
            )
            self.renting_invoice_ids |= invoice_id
            line.invoice_ids |= invoice_id
            invoice_id.action_post()
            renting_contract.create_payment_for_invoice(invoice_id=invoice_id)

    def generate_invoice(self):
        """Scheduler: Generate Invoice"""
        rc_ids = self.env["billboard.renting.contract"].search([
            ("contract_stage", "=", "running"), ("id", "=", 860)
        ])
        current_date = fields.Date.context_today(self)
        frequency_intervals = {
            "daily": timedelta(days=1),
            "quarterly": timedelta(days=90),
            "semiannually": timedelta(days=180)
        }

        for rc_id in rc_ids:
            for line in rc_id.billboard_asset_line:
                if line.is_onhold:
                    continue
                if not (line.start_date <= current_date <= line.end_date):
                    continue
                if line.billing_frequency == "monthly":
                    interval = relativedelta(months=1)
                elif line.billing_frequency == "yearly":
                    interval = relativedelta(years=1)
                else:
                    interval = frequency_intervals.get(line.billing_frequency)
                    if not interval:
                        continue
                if not line.prev_invoice_generated:
                    rc_id.generate_scheduled_multiple_invoice(renting_contract=rc_id, line=line)
                    rc_id.prev_invoice_generated = current_date
                    continue
                next_invoice_date = line.prev_invoice_generated + interval
                if next_invoice_date == current_date:
                    rc_id.generate_scheduled_multiple_invoice(renting_contract=rc_id, line=line)
                    rc_id.prev_invoice_generated = current_date

    def prepare_invoice_line(self, billboard):
        """prepare invoice line
        :param billboard:
        :return:
        """
        invoice_line = {
            "product_id": billboard.sale_line_id.product_id.id
            if billboard.sale_line_id else False,
            "ref": f"Billboard Renting Contract: {billboard.billboard_tag}"
                   + " : "
                   + self._get_billboard_address(billboard),
            "name": f"Billboard Renting Contract: {billboard.billboard_tag}"
                    + " : "
                    + self._get_billboard_address(billboard),
            "quantity": 1.0,
            "price_unit": billboard.net_price / billboard.sale_line_id.product_uom_qty
            if billboard.sale_line_id else billboard.net_price,
            "billboard_renting_contract_id": self.id,
            "billboard_id": billboard.billboard_asset_id.id,
        }
        return invoice_line

    def action_advance_payment(self, total_rental_price=None, total_discount=None, billboard_amount=None,
                               remaining_amount=None):
        if billboard_amount is None:
            billboard_amount = self.remaining_amt
        if remaining_amount is None:
            remaining_amount = self.remaining_amt
        if total_discount is None:
            total_discount = sum(self.billboard_asset_line.mapped('discount'))
        if total_rental_price is None:
            total_rental_price = sum(self.billboard_asset_line.mapped('rental_price'))

        advance_payment_wizard = self.env["billboard.advance.payment"].create(
            {
                "billboard_id": self.id,
                "total_discount": total_discount,
                "total_rental_price": total_rental_price,
                "billboard_amount": billboard_amount,
                "remaining_amt": remaining_amount,
                "advance_amount": self.advance_amount,
            }
        )
        return {
            'name': _('Advance Payment'),
            'res_model': 'billboard.advance.payment',
            "res_id": advance_payment_wizard.id,
            'view_mode': 'form',
            'context': {
                'active_model': 'account.asset',
                'active_ids': self.ids,
            },
            "target": "new",
            "type": "ir.actions.act_window",
        }

    def invoice(self, invoice_date, invoice_line_ids, asset_line):
        """prepare invoice
        :param invoice_date:
        :param invoice_line_ids:
        :return:
        """
        invoice = {
            "name": "/",
            "partner_id": self.partner_id.id,
            "invoice_date": invoice_date,
            "billboard_renting_contract_id": self.id,
            "invoice_line_ids": invoice_line_ids,
            "move_type": "out_invoice",
            "global_discount_rate": asset_line.discount,
            # "amount_untaxed": self.total_discount,
            "is_billboard_invoice": True,
        }
        return invoice

    def prepare_scheduler_invoice_line(self, billboard, renting_contract):
        """prepare invoice line from scheduler
        :param billboard:
        :param rc:
        :return:
        """
        invoice_line = {
            "product_id": billboard.sale_line_id.product_id.id
            if billboard.sale_line_id else False,
            "ref": f"Billboard Renting Contract: {billboard.billboard_tag}"
                   + " : "
                   + renting_contract._get_billboard_address(billboard),
            "name": f"Billboard Renting Contract: {billboard.billboard_tag}"
                    + " : "
                    + renting_contract._get_billboard_address(billboard),
            "quantity": 1.0,
            "price_unit": billboard.net_price / billboard.no_of_unit,
            "billboard_renting_contract_id": renting_contract.id,
            "billboard_id": billboard.billboard_asset_id.id,
            "billboard_contract_line_id": billboard.id,
        }
        return invoice_line

    def prepare_scheduler_invoice(
            self, rc, line, invoice_date, invoice_line_ids
    ):
        """prepare invoice from scheduler
        :param rc:
        :param invoice_date:
        :param invoice_line_ids:
        :return:
        """
        invoice = {
            "name": '/',
            "partner_id": rc.partner_id.id,
            "invoice_date": invoice_date,
            "billboard_renting_contract_id": rc.id,
            "invoice_line_ids": invoice_line_ids,
            "move_type": "out_invoice",
            "is_billboard_invoice": True,
        }
        return invoice

    # ================================ Smart Button Actions ================================
    def action_rc_invoice(self):
        """Action: All Renting Contract Invoices"""
        action = self.env.ref("account.action_move_out_invoice_type").read()[0]
        action["domain"] = [
            ("move_type", "=", "out_invoice"),
            ("is_billboard_invoice", "=", True),
            ("billboard_renting_contract_id", "=", self.id),
        ]
        action["context"] = {
            "default_move_type": "out_invoice",
            "create": False,
            "unlink": False,
            "edit": False,
        }
        return action

    def smart_btn_renewed_rc(self):
        """Action: All Renting Contract Renewed"""
        action = self.env.ref(
            "signjet_billboard.action_billboard_renting_contract"
        ).read()[0]
        action["domain"] = [("renewed_from_rc_id", "=", self.id)]
        action["context"] = {"create": False, "unlink": False}
        return action

    # ================================ Button Actions ================================
    def create_billboard_rc_line(self, billboard):
        renting_contract = {
            "serial_no": self.serial_no,
            "rc_id": self.id,
            "partner_id": self.partner_id.id,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "contract_stage": self.contract_stage,
            "asset_id": billboard.id,
            "name": self.name,
        }
        self.env["billboard.renting.contract.line"].sudo().create(renting_contract)

    def update_billboard_rc_line(self, billboard):
        for renting_contract in billboard.rc_line:
            if renting_contract.rc_id.id == self.id:
                renting_contract.contract_stage = self.contract_stage

    def action_booked(self):
        """Confirm Renting Contract"""
        self.is_booked = True
        self.contract_stage = "booked"
        if not self.billboard_asset_line:
            raise UserError(_("Please, add Billboard Asset to continue!"))
        for asset in self.billboard_asset_line:
            asset.billboard_asset_id.sudo().stage = "booked"
            asset.sudo().stage = "booked"
            asset.billboard_asset_id.sudo().rc_ids = [(4, self.id)]
            self.create_billboard_rc_line(billboard=asset.billboard_asset_id)
            self.billboard_asset_ids = [(4, asset.billboard_asset_id.id)]
            for renting_contract in asset.billboard_asset_id.rc_ids:
                if renting_contract.contract_stage == "rented":
                    asset.stage = "rented"
                    break
            for renting_contract in asset.billboard_asset_id.rc_line:
                if renting_contract.contract_stage == "rented":
                    asset.stage = "rented"
                    break

    def action_approve(self):
        """Approve the contract"""
        if self.contract_stage != "booked":
            raise UserError(_("Please refresh the page to continue!"))
        self.previous_stage = self.contract_stage
        self.contract_stage = "submitted_customer_approval"
        for asset in self.billboard_asset_line:
            self.update_billboard_rc_line(billboard=asset.billboard_asset_id)

    def action_customer_signed(self):
        """Contract signed the contract"""
        if self.contract_stage != "submitted_customer_approval":
            raise UserError(_("Please refresh the page to continue!"))
        return {
            "name": _("Renting Contract Signed"),
            "res_model": "rc.customer.signed",
            "view_mode": "form",
            "target": "new",
            "type": "ir.actions.act_window",
        }

    def prepare_invoice_payment_line(self, journal_id, amount):
        """
        prepare payment line
        """
        return {
            'date': fields.Date.context_today(self),
            'amount': abs(amount),
            'journal_id': journal_id.id,
            'payment_type': abs(amount) > 0 and 'inbound' or 'outbound',
            'partner_id': self.partner_id.id,
            'partner_type': 'customer',
            'payment_method_line_id': journal_id.inbound_payment_method_line_ids.id or
                                      journal_id.inbound_payment_method_line_ids.id,
            'company_id': self.company_id.id,
            'billboard_renting_contract_payment_id': self.id,
            'ref': _('Invoice Payment Entry for Billboard Renting %s', str(self.name))
        }

    def prepare_advance_and_invoice_adjustment_pay_debit_line(self, amount, debit_account):
        """
        return
        """
        return {
            'partner_id': self.partner_id.id,
            'account_id': debit_account,
            'debit': amount,
            'name': _('Advance And Invoice Adjustment payment Entry for billboard renting contract of '
                      'billboard renting contract %s', self.name),
        }

    def prepare_advance_and_invoice_adjustment_pay_credit_line(self, amount, credit_account):
        """
        return
        """
        return {
            'partner_id': self.partner_id.id,
            'account_id': credit_account,
            'credit': amount,
            'name': _('Advance And Invoice Adjustment payment Entry for billboard renting contract for'
                      ' billboard renting contract %s', self.name),
        }

    def manual_advance_payment_entry_vals(self, lines, journal_id):
        """
        prepare move
        """
        return {
            'ref': _('Advance And Invoice Adjustment payment Entry for billboard renting contract of %s', self.name),
            'date': fields.Date.context_today(self),
            'line_ids': lines,
            'journal_id': journal_id.id,
            'move_type': 'entry',
            'is_invoice_and_advance_adjustment_billboard_payment_entry': True,
            'advance_and_invoice_billboard_adj_id': self.id,
            'company_id': self.company_id.id
        }

    def create_advance_and_invoice_adjustment_payment_entry(self, amount, journal_id):
        """
        create manual payment entry for advance payment
        """
        line_list = []
        credit_account = self.company_id.billboard_virtual_account_id.id
        debit_account = self.company_id.billboard_advance_account_id.id
        debit_line = self.prepare_advance_and_invoice_adjustment_pay_debit_line(amount=amount,
                                                                                debit_account=debit_account)
        credit_line = self.prepare_advance_and_invoice_adjustment_pay_credit_line(amount=amount,
                                                                                  credit_account=credit_account)
        line_list.append((0, 0, debit_line))
        line_list.append((0, 0, credit_line))
        advance_entry_move = self.manual_advance_payment_entry_vals(lines=line_list, journal_id=journal_id)
        if credit_line and debit_line and advance_entry_move:
            move_id = self.env['account.move'].create(advance_entry_move)
            if move_id:
                move_id.sudo().action_post()
                self.manual_advance_payment_entry_ids = [(4, move_id.id)]

    def create_payment_for_invoice(self, invoice_id):
        """
        create payment for invoice adjustment
        """
        amount = sum(invoice_id.mapped('amount_total_signed'))
        journal_id = self.payment_method_id
        if self.advance_amount > self.invoice_paid_amt and self.total_rental_price > 0.0:
            if amount > (self.advance_amount - self.invoice_paid_amt - self.total_rental_price):
                amount = self.advance_amount - self.invoice_paid_amt
            else:
                amount = amount
            if amount > 0.0:
                payment_line = self.prepare_invoice_payment_line(journal_id=journal_id,
                                                                 amount=amount)
                payment = self.env['account.payment'].create(payment_line)
                payment.sudo().action_post()
                self.invoice_paid_amt += payment.amount
                invoice_id.sudo().action_post()
                move_lines = payment.line_ids.filtered(
                    lambda line: line.account_internal_type in ('receivable', 'payable') and not line.reconciled)
                for line in move_lines:
                    invoice_id.js_assign_outstanding_line(line.id)
                self.invoice_payment_ids = [(4, payment.id)]
                self.create_advance_and_invoice_adjustment_payment_entry(amount=amount, journal_id=journal_id)

    def action_contract_running(self):
        """Contract Running"""
        action = {
            "name": _("Running Wizard"),
            "res_model": "billboard.contract.running.wiz",
            "view_mode": "form",
            "type": "ir.actions.act_window",
            "context": {
                "default_billboard_contract_id": self.id,
            },
            "target": "new",
        }
        return action

    def action_generate_first_invoice(self):
        """Generate First at running stage Invoice On button click"""
        curr_invoice_line = []
        if self.contract_stage != "running":
            raise UserError(_("Please refresh your page!"))
        if not self.prev_invoice_generated:
            for asset in self.billboard_asset_line:
                invoice_line_ids = self.prepare_invoice_line(billboard=asset)
                curr_invoice_line.append((0, 0, invoice_line_ids))
            line = self.invoice(
                invoice_date=self.start_date, invoice_line_ids=curr_invoice_line
            )
            invoice_id = self.env["account.move"].create(line)
            self.renting_invoice_ids |= invoice_id
            invoice_id.action_post()
            self.create_payment_for_invoice(invoice_id=invoice_id)
            self.prev_invoice_generated = self.start_date

    def update_billboard_slot_line_availability(self, asset):
        contract_slot_ids = self.billboard_asset_line.mapped('billboard_contract_slot_ids')
        if asset and contract_slot_ids:
            for slot in asset.billboard_slot_line:
                if slot in contract_slot_ids:
                    if self.contract_stage in ("terminated", "cancelled"):
                        slot.status = "available"
                        slot.sale_id = False
                    elif self.contract_stage == "running":
                        slot.status = "rented"
                        slot.sale_id = self.sale_id
                    else:
                        slot.status = "booked"
                        slot.sale_id = self.sale_id

    def update_billboard_asset(self, asset):
        self.update_billboard_slot_line_availability(asset=asset.billboard_asset_id)
        asset.billboard_asset_id.sudo().stage = "available"
        asset.sudo().stage = "available"
        self.update_billboard_rc_line(billboard=asset.billboard_asset_id)
        for rc in asset.billboard_asset_id.rc_ids:
            if rc.contract_stage == "booked":
                asset.billboard_asset_id.sudo().stage = "booked"
                asset.sudo().stage = "booked"
            if rc.contract_stage == "rented":
                asset.billboard_asset_id.sudo().stage = "rented"
                asset.sudo().stage = "rented"
                break

    def action_contract_terminated(self):
        """Contract Running"""
        # if self.contract_stage != "running":
        #     raise UserError(_("Please refresh the page to continue!"))
        self.previous_stage = self.contract_stage
        self.contract_stage = "terminated"
        for asset in self.billboard_asset_line:
            self.update_billboard_asset(asset=asset)
            asset.billboard_asset_id.sudo().stage = "available"
            asset.sudo().stage = "available"
            self.update_billboard_rc_line(billboard=asset.billboard_asset_id)
            for renting_contract in asset.billboard_asset_id.rc_ids:
                if renting_contract.contract_stage == "booked":
                    asset.billboard_asset_id.sudo().stage = "booked"
                    asset.sudo().stage = "booked"
                if renting_contract.contract_stage == "rented":
                    asset.billboard_asset_id.sudo().stage = "rented"
                    asset.sudo().stage = "rented"
                    break

    def action_terminate_billboard_scheduled_contract(self):
        billboard_contracts = self.env["billboard.renting.contract"].search(
            [("contract_stage", "in", ("draft", "running"))]
        )
        if billboard_contracts:
            for contract in billboard_contracts:
                for line in contract.billboard_asset_line:
                    if line.end_date and line.end_date < fields.Date.context_today(self):
                        line.stage = "terminated"
                if all(line.end_date and line.end_date < fields.Date.context_today(self)
                       for line in contract.billboard_asset_line):
                    contract.contract_stage = "terminated"
                if contract.end_date and contract.end_date < fields.Date.context_today(self):
                    contract.contract_stage = "terminated"


    def action_contract_cancelled(self):
        """Contract Running"""
        self.previous_stage = self.contract_stage
        self.contract_stage = "cancelled"
        for asset in self.billboard_asset_line:
            asset.billboard_asset_id.sudo().stage = "available"
            asset.sudo().stage = "available"
            self.update_billboard_rc_line(billboard=asset.billboard_asset_id)
            for renting_contract in asset.billboard_asset_id.rc_ids:
                if renting_contract.contract_stage == "booked":
                    asset.billboard_asset_id.sudo().stage = "booked"
                    asset.sudo().stage = "booked"
                if renting_contract.contract_stage == "rented":
                    asset.billboard_asset_id.sudo().stage = "rented"
                    asset.sudo().stage = "rented"
                    break
            for renting_contract in asset.billboard_asset_id.rc_line:
                if renting_contract.contract_stage == "booked":
                    asset.billboard_asset_id.sudo().stage = "booked"
                    asset.sudo().stage = "booked"
                if renting_contract.contract_stage == "rented":
                    asset.billboard_asset_id.sudo().stage = "rented"
                    asset.sudo().stage = "rented"
                    break

    def create_renew_contract(self):
        """Create Renew Contract"""
        asset_line = self.create_billboard_asset_line()
        renting_contract = {
            "default_name": f"{self.name}(renew{str(self.renew_sequence)})",
            "default_partner_id": self.partner_id.id if self.partner_id else [],
            "default_company_id": self.company_id.id,
            "default_branch_id": self.branch_id.id if self.branch_id else [],
            "default_billing_frequency": self.billing_frequency
            if self.billing_frequency
            else "",
            "default_start_date": fields.Date.context_today(self),
            "default_billboard_discount": self.billboard_discount,
            "default_billboard_asset_line": asset_line,
            "default_renewed_from_rc_id": self.id,
        }
        self.renew_sequence += 1
        return renting_contract

    def create_billboard_asset_line(self):
        """Create renewed billboard asset line"""
        line = []
        for asset in self.billboard_asset_line:
            asset_id = Command.create(
                {
                    "billboard_asset_id": asset.billboard_asset_id.id,
                    "discount": asset.discount,
                    "rental_price": asset.billboard_asset_id.rental_price,
                    "net_price": asset.net_price,
                    "installed_date": asset.installed_date,
                    "billboard_tag": asset.billboard_tag,
                    "stage": asset.billboard_asset_id.stage,
                    "billboard_type_id": asset.billboard_type_id.id,
                    "size_id": asset.size_id.id,
                    "is_rental_price_editable": asset.is_rental_price_editable,
                }
            )
            # asset_id.onchange_asset_id()
            line.append(asset_id)
        return line

    def action_renew_contract(self):
        """Button to Renew the current Renting Contract"""
        action = {
            "name": _("Renew Contract"),
            "res_model": "billboard.renting.contract",
            "view_mode": "form",
            "context": self.create_renew_contract(),
            "type": "ir.actions.act_window",
        }
        return action

    def action_onhold(self):
        """On hold the agreement"""
        self.previous_stage = self.contract_stage
        self.contract_stage = "on_hold"
        self.create_history(
            history_type="on_hold", remark=f"Billboard Renting Contract is on-hold."
        )
        for asset in self.billboard_asset_line:
            self.update_billboard_rc_line(billboard=asset.billboard_asset_id)

    def action_resume(self):
        """Resume the agreement"""
        # self.previous_stage = self.contract_stage
        self.contract_stage = self.previous_stage
        self.create_history(
            history_type="resume", remark=f"Billboard Renting Contract is resume."
        )
        for asset in self.billboard_asset_line:
            self.update_billboard_rc_line(billboard=asset.billboard_asset_id)

    def cancel_all_renting_invoices(self):
        if self.contract_stage == "draft":
            invoice_ids = self.env["account.move"].sudo().search(
                [
                    ("move_type", "=", "out_invoice"),
                    ("is_billboard_invoice", "=", True),
                    ("billboard_renting_contract_id", "=", self.id),
                ]
            )
            if invoice_ids:
                for invoice in invoice_ids:
                    invoice.button_draft()
                    invoice.button_cancel()

    def action_reset_to_draft(self):
        """Reset to Draft Stage"""
        if self.contract_stage == "draft":
            raise UserError(_("Please refresh the page!"))
        if self.billboard_asset_line:
            for line in self.billboard_asset_line:
                line.sudo().stage = "available"
                line.billboard_asset_id.sudo().stage = "available"
        self.contract_stage = "draft"
        for asset in self.billboard_asset_line:
            self.update_billboard_rc_line(billboard=asset.billboard_asset_id)
            self.update_billboard_slot_line_availability(asset=asset.billboard_asset_id)
            if any(slot.status == "available" for slot in asset.billboard_asset_id.billboard_slot_line):
                asset.billboard_asset_id.sudo().stage = "available"
            if all(slot.status == "booked" for slot in asset.billboard_asset_id.billboard_slot_line):
                asset.billboard_asset_id.sudo().stage = "booked"
            if all(slot.status == "rented" for slot in asset.billboard_asset_id.billboard_slot_line):
                asset.billboard_asset_id.sudo().stage = "rented"
        self.cancel_all_renting_invoices()

    def get_billboard_date_range(self):
        """Returns (min_start_date, max_end_date) from billboard_asset_line"""
        self.ensure_one()
        lines = self.billboard_asset_line
        if lines:
            start_date = min(lines.mapped('start_date'))
            end_date = max(lines.mapped('end_date'))
            print("Start Date: ", start_date, "End Date: ", end_date)
            return start_date, end_date
        return False, False


class BillboardAssetRcLine(models.Model):
    _name = "billboard.asset.rc.line"
    _rec_name = "billboard_asset_id"
    _description = "Billboard Asset Renting Contract Line"

    billboard_asset_id = fields.Many2one("account.asset", copy=False)
    rc_id = fields.Many2one("billboard.renting.contract", copy=False)
    billboard_tag = fields.Char(copy=False, string="Tag")
    billboard_type_id = fields.Many2one(
        "billboard.type.master", string="Billboard Type"
    )
    sale_order_id = fields.Many2one("sale.order")
    sale_line_id = fields.Many2one("sale.order.line")
    rental_price = fields.Float("Rental Price")
    size_id = fields.Many2one("billboard.size.master", string="Size")
    street_id = fields.Many2one("res.street")
    installed_date = fields.Date("Installed Date")
    stage = fields.Selection(
        [
            ("available", "Available"),
            ("booked", "Booked"),
            ("rented", "Rented"),
            ("damaged", "Damaged"),
            ("cancelled", "Cancelled"),
            ("terminated", "Terminated"),
        ], string="Stage"
    )
    billing_frequency = fields.Selection(
        [
            ("daily", "Daily"),
            ("monthly", "Monthly"),
            ("quarterly", "Quarterly"),
            ("semiannually", "SemiAnnually"),
            ("yearly", "Yearly"),
        ], string="Billing Frequency"
    )
    start_date = fields.Date(tracking=True, string="Start Date")
    end_date = fields.Date(tracking=True, string="End Date")
    prev_invoice_generated = fields.Date(string="Previous Invoice Generated")
    discount = fields.Float()
    is_line_discount = fields.Boolean()
    net_price = fields.Float()
    is_onhold = fields.Boolean()
    is_resume = fields.Boolean()
    is_rental_price_editable = fields.Boolean()
    status = fields.Char()
    no_of_unit = fields.Integer(string="No of Unit")
    billboard_contract_slot_ids = fields.Many2many("billboard.slot.line")
    product_component_ids = fields.Many2many(
        "product.product",
        "billboard_product_component_rel",
        string="Components"
    )
    billboard_product_component_ids = fields.Many2many(
        "product.component",
        "billboard_product_components_rel",
        string="Components"
    )
    is_billboard_outsource = fields.Boolean(default=False)
    outsource_po_ids = fields.Many2one("purchase.order", string="Outsource PO's")
    invoice_ids = fields.Many2many("account.move", "contract_line_invoice_rel", string="Invoices")

    @api.onchange("billboard_asset_id", "billboard_asset_id.stage")
    def onchange_asset_id(self):
        if self.billboard_asset_id:
            self.billboard_tag = self.billboard_asset_id.billboard_tag
            self.billboard_type_id = self.billboard_asset_id.billboard_type_id.id
            self.rental_price = self.billboard_asset_id.rental_price
            self.size_id = self.billboard_asset_id.size_id.id
            self.installed_date = self.billboard_asset_id.installed_date
            self.stage = self.billboard_asset_id.stage
            self.net_price = self.rental_price
            if self.rc_id.billboard_asset_line.billboard_asset_id:
                asset_list = []
                for asset in self.rc_id.billboard_asset_line:
                    asset_list.append(asset.billboard_asset_id)
                count_assets = Counter(asset_list)
                for asset in count_assets:
                    if count_assets[asset] > 2:
                        raise UserError(
                            _(f"The Billboard Asset {asset.name} is already linked.")
                        )

    @api.onchange("discount", "rental_price")
    def onchange_discount(self):
        """Validate Discount cannot be negative and greater than 100%"""
        if self.rental_price < 0:
            raise UserError(_("Rental Price cannot be negative!"))
        if self.discount < 0:
            raise UserError(
                _(
                    f"Discount Amount for {self.billboard_asset_id.name} cannot be negative!"
                )
            )
        if self.discount > self.rental_price:
            raise UserError(
                _(
                    f"Discount Amount for {self.billboard_asset_id.name} cannot be greater than "
                    f"Rental Price {self.rental_price}!"
                )
            )
        self.net_price = self.rental_price
        if self.rc_id.billboard_asset_line.billboard_asset_id:
            if self.discount != 0:
                if self.discount != self.rc_id.billboard_discount / len(
                        self.rc_id.billboard_asset_line.billboard_asset_id
                ):
                    self.is_line_discount = True
                    self.net_price = self.rental_price

    def action_remove_billboard(self):
        """Remove Billboard in renting contract from billboard asset line"""
        if self.rc_id and self.rc_id.contract_stage == "booked":
            self.billboard_asset_id.stage = "available"
            self.billboard_asset_id.sudo().stage = "available"
            self.sudo().stage = "available"
            self.rc_id.billboard_asset_ids = [(4, self.billboard_asset_id.id)]
            self.billboard_asset_id.rc_ids = [(3, self.rc_id.id)]

            for asset in self.billboard_asset_id.rc_line:
                if asset.rc_id.id == self.rc_id.id:
                    asset.sudo().end_date = fields.Date.context_today(self)

                    asset.contract_stage = "draft"
                if asset.contract_stage == "booked":
                    self.billboard_asset_id.sudo().stage = "booked"
                if asset.contract_stage == "rented":
                    self.billboard_asset_id.sudo().stage = "rented"
                    break
            self.rc_id.create_history(
                history_type="removed",
                remark=f"Billboard Asset {self.billboard_asset_id.name} "
                       f"is Removed from Renting Contract.",
            )

            self.rc_id.billboard_asset_line = [(3, self.id)]

    def action_onhold_billboard(self):
        """Onhold Billboard in renting contract from billboard asset line"""
        if self.rc_id and self.rc_id.contract_stage == "running":
            self.is_onhold = True
            self.is_resume = False
            self.billboard_asset_id.rc_ids = [(3, self.rc_id.id)]
            self.rc_id.create_history(
                history_type="on_hold",
                remark=f"Billboard Asset {self.billboard_asset_id.name} "
                       f"is on-hold.",
            )

    def action_resume(self):
        """Resume Onhold Asset"""
        if self.is_onhold:
            self.is_resume = True
            self.is_onhold = False
            self.billboard_asset_id.rc_ids = [(4, self.rc_id.id)]
            self.rc_id.create_history(
                history_type="resume",
                remark=f"Billboard Asset {self.billboard_asset_id.name} "
                       f"is resumed.",
            )


class ContractPaymentTermLine(models.TransientModel):
    _name = "billboard.contract.payment.term.line"

    renting_contract_id = fields.Many2one("billboard.renting.contract", string="Renting Contract")
    payment_date = fields.Date(string="Payment Date")
    payment_amount = fields.Float(string="Payment Amount")
