# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
import base64
from collections import defaultdict
from io import BytesIO
from datetime import datetime
from dateutil.relativedelta import relativedelta
import qrcode
from odoo import _, api, fields, models, Command
from odoo.exceptions import UserError, ValidationError


class SaleOrder(models.Model):
    _inherit = "sale.order"

    billboard_order_type = fields.Selection(
        [
            ("quotation", "Quotation"),
            ("order", "Sale Order"),
        ],
        string="Billboard Order Type"
    )
    billboard_contract_ids = fields.Many2many("billboard.renting.contract", string="Billboard Contracts")
    is_billboard_renting_contract_created = fields.Bo<PERSON>an(default=False)
    billboard_attachment_url = fields.Char(string="Billboard Attachment URL")
    is_billboard_outsource = fields.Boolean(default=False)
    back_date_contract_reason = fields.Char(string="Back Date Contract Reason", tracking=True)

    def validate_billboard_date(self, line):
        if line:
            billboard = self.env["account.asset"].sudo().search(
                [
                    ("is_billboard", "=", True),
                    ("is_virtual_billboard", "!=", True),
                    ("id", "=", line.asset_id.id),
                    ("billboard_type_id", "=", line.billboard_type_id.id),
                    ("size_id", "=", line.billboard_size_id.id),
                    ('district_id', '=', line.billboard_district_id.id),
                ], limit=1
            )
            if billboard:
                bill_end_date = None
                for rc in billboard.rc_line:
                    bill_end_date = rc.end_date
                if bill_end_date and line.from_date <= bill_end_date:
                    raise UserError(_(
                        f"{line.asset_id.billboard_tag} is not available for this date range"
                    ))

    def validate_already_created_contract(self, line):
        if line:
            renting_contract = self.env["billboard.asset.rc.line"].sudo().search(
                [
                    ("sale_order_id", "=", self.id),
                    ("sale_line_id", "=", line.id),
                ]
            )
            if renting_contract and renting_contract:
                if all(contract.rc_id.contract_stage != "cancelled" for contract in renting_contract):
                    return True
        return False

    def update_billboard_slot_status(self, line, billboard_slot_ids):
        if not (line and billboard_slot_ids):
            return
        asset = line.asset_id
        if not asset.is_virtual_billboard:
            slot_names = [slot.name for slot in billboard_slot_ids]
            slot_lines = self.env["billboard.slot.line"].sudo().search([
                ("billboard_slot_id", "=", asset.id),
                ("name", "in", slot_names),
            ])
            slot_lines.sudo().update({"status": "booked"})
            billboard_slot_ids.sudo().update({"status": "booked"})
            if all(
                slot.status in ("booked", "rented") for slot in line.asset_id.billboard_slot_line
            ):
                asset.stage = "booked"
            if all(
                slot.status == "rented" for slot in line.asset_id.billboard_slot_line
            ):
                asset.stage = "rented"
            if any(
                    slot.status == "available" for slot in line.asset_id.billboard_slot_line
            ):
                asset.stage = "available"
        elif asset.is_virtual_billboard :
            billboard_slot_ids.sudo().update({"status": "available"})
            asset.stage = "available"

    def check_billboard_printing_required(self, line):
        if line.is_billboard_product:
            printing_product = self.env["sale.order.line"].sudo().search(
                [
                    ("product_for_billboard_line_id", "=", line.asset_id.id),
                    ("printing_for_billboard_line_id", "=", line.id),
                    ("order_id", "=", line.order_id.id),
                    ("charged_for_sale_line_id", "=", False)
                ], limit=1
            )
            if printing_product:
                return printing_product
        return False

    def _prepare_psale_line(self):
        """Prepare Printing Sale Update Order line"""
        lst = []
        if self.order_line:
            for line in self.order_line.filtered(lambda line: not line.qty_invoiced):
                printing_product = self.check_billboard_printing_required(line=line)
                task_ids = self.get_so_task(sale_line=line)
                if task_ids:
                    lst.append(Command.create({
                        'product_id': line.product_id.id,
                        'sale_line_id': line.id,
                        'name': line.name,
                        'height': line.height,
                        'width': line.width,
                        'asset_id': line.asset_id.id,
                        'billboard_slot_ids': line.billboard_slot_ids.ids,
                        "frequency_type": line.rental_frequency,
                        "start_date": line.from_date,
                        "end_date": line.to_date,
                        "no_of_unit": line.no_of_unit,
                        'printing_product_id': printing_product.product_id.id if printing_product else False,
                        'design_required': printing_product.design_required
                        if printing_product else line.design_required,
                        'installation_required': printing_product.installation_required
                        if printing_product else line.installation_required,
                        'sold_as': line.sold_as,
                        'qty': line.product_uom_qty,
                        'price': line.price_unit,
                        'is_component_product': True if line.is_component_applicable else False,
                        'product_component_ids': printing_product.product_component_ids.ids
                        if printing_product else line.product_component_ids.ids,
                        'is_page_type': True if line.is_page_type else False,
                        'pages': line.pages,
                        'is_price_editable': line.is_price_editable,
                        'is_outsource': line.is_outsource,
                        'is_billboard_product': line.is_billboard_product,
                        'is_charged_product': True if line.charged_for_sale_line_id else False,
                        'printing_required': 'yes' if printing_product else False
                    }))
                else:
                    lst.append(Command.create({
                        'product_id': line.product_id.id,
                        'sale_line_id': line.id,
                        'name': line.name,
                        'height': line.height,
                        'width': line.width,
                        'asset_id': line.asset_id.id,
                        'billboard_slot_ids': line.billboard_slot_ids.ids,
                        "frequency_type": line.rental_frequency,
                        "start_date": line.from_date,
                        "end_date": line.to_date,
                        "no_of_unit": line.no_of_unit,
                        'printing_product_id': printing_product.product_id.id if printing_product else False,
                        'design_required': printing_product.design_required
                        if printing_product else line.design_required,
                        'installation_required': printing_product.installation_required
                        if printing_product else line.installation_required,
                        'sold_as': line.sold_as,
                        'qty': line.product_uom_qty,
                        'price': line.price_unit,
                        'product_component_ids': printing_product.product_component_ids.ids
                        if printing_product else line.product_component_ids.ids,
                        'default_product_component_ids': line.product_component_ids.ids,
                        'is_component_product': True if line.is_component_applicable else False,
                        'is_page_type': True if line.is_page_type else False,
                        'pages': line.pages,
                        'is_price_editable': line.is_price_editable,
                        'is_outsource': line.is_outsource,
                        'is_billboard_product': line.is_billboard_product,
                        'is_charged_product': True if line.charged_for_sale_line_id else False,
                        'printing_required': 'yes' if printing_product else False
                    }))
        return lst

    def prepare_renting_line_details(self):
        line_list = []
        for line in self.order_line:
            if line.asset_id:
                line_list.append(Command.create(
                    {
                        "billboard_type_id": line.billboard_type_id.id,
                        "billboard_asset_id": line.asset_id.id,
                        "size_id": line.billboard_size_id.id,
                        "billboard_tag": line.asset_id.billboard_tag,
                        "rental_price": line.price_unit * line.product_uom_qty,
                        "net_price": line.price_subtotal,
                        "discount": (line.price_unit * line.product_uom_qty) - line.price_subtotal,
                        "stage": "booked",
                        "sale_order_id": self.id,
                        "billboard_contract_slot_ids": line.billboard_slot_ids.ids,
                        "sale_line_id": line.id,
                        "is_billboard_outsource": True if self.is_outsource else False,
                    }
                ))
                self.update_billboard_slot_status(
                    line=line, billboard_slot_ids=line.billboard_slot_ids
                )
        return line_list

    def get_billboard_printing_product_sequence(self):
        """Compute order line sequence"""
        if self.order_line:
            for line in self.order_line:
                if line.product_for_billboard_line_id or line.printing_for_billboard_line_id:
                    line.sequence = line.printing_for_billboard_line_id.sequence

    @api.constrains("order_line")
    def order_line_change_product(self):
        res = super().order_line_change_product()
        self.get_billboard_printing_product_sequence()
        return res

    def get_printing_product_components(self, line):
        if line:
            printing_product = self.env["sale.order.line"].sudo().search(
                [
                    ("product_for_billboard_line_id", "=", line.asset_id.id),
                    ("printing_for_billboard_line_id", "=", line.id),
                    ("order_id", "=", line.order_id.id),
                    ("charged_for_sale_line_id", "=", False)
                ], limit=1
            )
            if printing_product:
                return printing_product.product_component_ids
        return False

    def prepare_sale_order_renting_contract(self):
        renting_contract_line = []
        for line in filter(
                lambda ln: (ln.asset_id and ln.is_billboard_product
                            and not ln.renting_contract_created), self.order_line
        ):
            if line and line.asset_id:
                printing_product_components = self.get_printing_product_components(line=line)
                renting_contract_line.append(Command.create(
                    {
                        "billboard_type_id": line.billboard_type_id.id,
                        "billboard_asset_id": line.asset_id.id,
                        "no_of_unit": line.no_of_unit,
                        "size_id": line.billboard_size_id.id,
                        "street_id": line.billboard_street_id.id,
                        "billboard_tag": line.asset_id.billboard_tag,
                        "rental_price": line.price_subtotal,
                        "billing_frequency": line.rental_frequency,
                        "start_date": line.from_date,
                        "end_date": line.to_date,
                        "net_price": line.price_subtotal - line.line_total_discount,
                        "discount": line.line_total_discount,
                        "stage": "booked",
                        "sale_order_id": self.id,
                        "sale_line_id": line.id,
                        "billboard_contract_slot_ids": line.billboard_slot_ids.ids if line.billboard_slot_ids else False,
                        "billboard_product_component_ids": printing_product_components.ids if printing_product_components else False,
                    }
                ))
                self.update_billboard_slot_status(
                    line=line, billboard_slot_ids=line.billboard_slot_ids
                )
        return renting_contract_line

    def action_view_billboard_renting_contract(self):
        """Action:Billboard Renting Contract Smart Button"""
        action = (
            self.env.ref("signjet_billboard.action_billboard_renting_contract")
            .sudo()
            .read()[0]
        )
        action["domain"] = [
            ("sale_id", "=", self.id),
            ("company_id", "=", self.company_id.id),
        ]
        return action

    def create_billboard_renting_contract(self, renting_lines):
        if renting_lines:
            renting_contract = {
                "partner_id": self.partner_id.id,
                "sale_id": self.id,
                "contract_stage": "draft",
                "name": self.partner_id.name + " " + "(" + self.name + ")",
                "branch_id": self.branch_id.id,
                "company_id": self.company_id.id,
                "is_booked": True,
                "billboard_asset_line": renting_lines,
                "is_printing_sale_billboard": True,
                "billboard_attachment_url": self.billboard_attachment_url,
            }
            contract_created = self.env["billboard.renting.contract"].sudo().create(renting_contract)
            if contract_created:
                self.billboard_contract_ids |= contract_created
                self.is_billboard_renting_contract_created = True
            return contract_created
        return False

    # def get_billboard_printing_line_name(self):
    #     for line in self.order_line:
    #         if (line.product_id and line.is_printing_product and
    #                 line.printing_for_billboard_line_id and not line.charged_for_sale_line_id):
    #             height_width = (' " ' + "{:,.2f}".format(float(round(line.width, 2))) + '*'
    #                             + "{:,.2f}".format(float(round(line.height, 2))) + ' ' + '*'
    #                             + "{:,.2f}".format(
    #                         float(round(len(line.printing_for_billboard_line_id.billboard_slot_ids), 2))) + ' ' + '=' + ' '
    #                                                                                                         "{:,.2f}".format(
    #                         float(round(
    #                             line.width * line.height * len(line.printing_for_billboard_line_id.billboard_slot_ids),
    #                             2))) + 'm2' + ' " ')
    #             page = ""
    #             if line.product_id:
    #                 line.name = line.product_id.name + ' ' + height_width
    #             if line.pages:
    #                 page = ',' + ' ' + str(int(line.pages)) + ' ' + 'Pages'
    #             if line.product_component_ids:
    #                 line.name = line.product_id.name + ' (' + ', '.join(
    #                     line.product_component_ids.mapped('name')) + page + ')' + height_width
    #             if line.product_component_ids and any(
    #                     product_comp.is_height_and_width_applicable for product_comp in line.product_component_ids
    #             ):
    #                 line.name = line.product_id.name + ' (' + ', '.join(
    #                     line.product_component_ids.mapped('name')) + page


    def get_billboard_printing_line_name(self):
        """
        Generate the name for billboard printing lines based on product, dimensions, components, pages, and sides.
        """
        # Dictionary to convert number of sides to words
        num_to_words = {
            1: "One Side",
            2: "Two Sides",
            3: "Three Sides",
            4: "Four Sides",
            5: "Five Sides",
            6: "Six Sides",
            7: "Seven Sides",
            8: "Eight Sides",
            9: "Nine Sides",
            10: "Ten Sides"
        }

        for line in self.order_line:
            if (line.product_id and line.is_printing_product and
                    line.printing_for_billboard_line_id and not line.charged_for_sale_line_id):
                slot_length = len(line.printing_for_billboard_line_id.billboard_slot_ids) if line.printing_for_billboard_line_id.billboard_slot_ids else 1
                height_width = (' "' + "{:,.2f}".format(float(round((line.width / slot_length), 2))) + '*' +
                                "{:,.2f}".format(float(round(line.height, 2))) + ' ' + '*' +
                                "{:,.2f}".format(
                                    float(round(len(line.printing_for_billboard_line_id.billboard_slot_ids),
                                                2))) + ' ' + '=' + ' ' +
                                "{:,.2f}".format(
                                    float(round(
                                        (line.width / slot_length) * line.height * len(
                                            line.printing_for_billboard_line_id.billboard_slot_ids),
                                        2))) + 'm2' + ' "')

                page = ""
                if line.pages:
                    page = ',' + ' ' + str(int(line.pages)) + ' ' + 'Pages'

                if line.product_id:
                    line.name = line.product_id.name + ' ' + height_width

                if line.product_component_ids or line.printing_for_billboard_line_id:
                    num_sides = len(line.printing_for_billboard_line_id.billboard_slot_ids)
                    side_text = num_to_words.get(num_sides, f"{num_sides} Side")  # Fallback for numbers > 10
                    components = ' '.join(line.product_component_ids.mapped('name'))
                    line.name = f"{line.product_id.name} ({components}{page}, {side_text}) {height_width}"

                if (line.product_component_ids or line.printing_for_billboard_line_id and any(
                        product_comp.is_height_and_width_applicable for product_comp in line.product_component_ids))\
                        or line.printing_for_billboard_line_id:
                    num_sides = len(line.printing_for_billboard_line_id.billboard_slot_ids)
                    side_text = num_to_words.get(num_sides, f"{num_sides} Side")
                    components = ' '.join(line.product_component_ids.mapped('name'))
                    line.name = f"{line.product_id.name} ({components}{page}, {side_text}) {height_width}"

    def get_existing_renting_contract(self):
        if self.order_line:
            existing_contract = self.env["billboard.renting.contract"].sudo().search(
                [
                    ("sale_id", "=", self.id),
                    ("is_printing_sale_billboard", "=", True),
                    ("contract_stage", "=", "draft"),
                ], limit=1
            )
            return existing_contract
        return False

    def add_existing_contract_line(self, existing_contract, renting_contract_lines):
        if existing_contract and renting_contract_lines:
            existing_contract.update({"billboard_asset_line": renting_contract_lines})
            return existing_contract
        return False

    def prepare_billboard_renting_contract(self):
        if self.order_line:
            renting_contract_lines = self.prepare_sale_order_renting_contract()
            if renting_contract_lines:
                existing_contract = self.get_existing_renting_contract()
                if existing_contract:
                    contract_created = self.add_existing_contract_line(
                        existing_contract=existing_contract, renting_contract_lines=renting_contract_lines
                    )
                else:
                    contract_created = self.create_billboard_renting_contract(renting_lines=renting_contract_lines)
                if contract_created:
                    for line in filter(
                            lambda ln: ln.asset_id and ln.is_billboard_product, self.order_line
                    ):
                        line.renting_contract_created = True
                    return contract_created
        return False

    def add_asset_contract_line(self, line):
        if line and line.asset_id:
            billboard_contract_lines = self.env["billboard.asset.rc.line"].sudo().search(
                [
                    ("rc_id.sale_id", '=', self.id),
                    ("rc_id.is_printing_sale_billboard", '=', True),
                    ("rc_id.contract_stage", '=', "draft"),
                    ("rc_id.partner_id", '=', self.partner_id.id),
                    ("rc_id.billing_frequency", '=', line.rental_frequency),
                    ("sale_line_id", '=', line.id),
                ]
            )
            if not billboard_contract_lines:
                billboard_contract = self.env["billboard.renting.contract"].sudo().search(
                    [
                        ("sale_id", '=', self.id),
                        ("is_printing_sale_billboard", '=', True),
                        ("contract_stage", '=', "draft"),
                        ("partner_id", '=', self.partner_id.id),
                        ("billing_frequency", '=', line.rental_frequency),
                    ], limit=1
                )
                contract_add_line_list = []
                if billboard_contract:
                    contract_add_line_list.append(
                        Command.create(
                            {
                                "billboard_type_id": line.billboard_type_id.id,
                                "billboard_asset_id": line.asset_id.id,
                                "size_id": line.billboard_size_id.id,
                                "street_id": line.billboard_street_id.id,
                                "billboard_tag": line.asset_id.billboard_tag,
                                "rental_price": line.price_unit * line.product_uom_qty,
                                "net_price": line.price_subtotal,
                                "discount": line.line_total_discount,
                                "stage": "booked",
                                "sale_order_id": self.id,
                                "sale_line_id": line.id,
                                "billboard_contract_slot_ids": line.billboard_slot_ids.ids,
                            }
                        )
                    )
                    self.update_billboard_slot_status(line=line, billboard_slot_ids=line.billboard_slot_ids)
                billboard_contract.billboard_asset_line = contract_add_line_list

    @api.constrains("order_line")
    def get_billboard_product_outsource_status(self):
        allowed_companies = self.env.context.get("allowed_company_ids", [])
        if self.order_line:
            for line in self.order_line:
                if line.is_billboard_product and line.asset_id:
                    if line.asset_id.company_id.id not in allowed_companies:
                        line.is_outsource = True
                        line.outsource_to = "regional"
                    elif line.asset_id.is_virtual_billboard:
                        line.is_outsource = True
                        if not line.outsource_to:
                            line.outsource_to = "local"
                    else:
                        line.is_outsource = False
                        line.outsource_to = False
            if any(line.is_outsource for line in self.order_line):
                self.is_outsource = True
                self.is_outsource_sale_order = True
            outsource_order_lines = list(filter(lambda line: line.is_outsource, self.order_line))
            # if outsource_order_lines:
            #     if all(line.asset_id for line in outsource_order_lines):
            #         self.sale_outsource_to = "regional"


    @api.constrains("order_line", "order_line.asset_id")
    def update_billboard_contract_line(self):
        if self.billboard_contract_ids and not all(
                contract.contract_stage == "cancel" for contract in self.billboard_contract_ids
        ):
            order_line_billboard = list(filter(lambda line: line.id, self.order_line))
            if order_line_billboard:
                for contract in self.billboard_contract_ids:
                    for contract_line in contract.billboard_asset_line:
                        if (not contract_line.sale_line_id or contract_line.sale_line_id.id
                                not in [line.id for line in order_line_billboard]):
                            contract_line.unlink()

    @api.constrains("order_line", "order_line.asset_id")
    def validate_order_line_billboard_asset(self):
        if self.billboard_contract_ids:
            if all(contract.contract_stage != "draft" for contract in self.billboard_contract_ids):
                raise UserError(_(f"You cannot add or remove the billboard asset from line as "
                                  f"the billboard contract is not in draft stage"))

    def create_outsource_po_for_other_company_billboard(self):
        if self.billboard_contract_ids:
            allowed_companies = self.env.context.get("allowed_company_ids", [])
            for contract in filter(
                lambda ct: ct.contract_stage in ("draft", "running"), self.billboard_contract_ids
            ):
                if any(line.billboard_asset_id.company_id.id not in allowed_companies
                       for line in contract.billboard_asset_line):
                    contract.update({"is_billboard_outsource": True, "vendor_id": self.supplier_id.id})
                    contract.billboard_asset_line.update({"is_billboard_outsource": True})
                    contract.action_billboard_outsource()

    def _prepare_order_line_outsource(self):
        """
        Override Prepare Outsource Order Line
        """
        if any(line.is_billboard_product for line in self.order_line):
            order = []
            if self.order_line:
                for line in self.order_line.filtered(
                        lambda line: not line.charged_for_sale_line_id
                                     and line.is_outsource
                                     and any((line.product_id.product_tmpl_id.is_printing_product,
                                              line.product_id.product_tmpl_id.is_printing_type))
                                     and not line.product_id.product_tmpl_id.is_designing_product
                                     and not line.product_id.product_tmpl_id.is_installation_product
                ):
                    outsource_to = False
                    if line.is_billboard_product and not line.asset_id.is_virtual_billboard:
                        outsource_to = "regional"
                    if line.is_billboard_product and line.asset_id.is_virtual_billboard:
                        outsource_to = line.outsource_to
                    supplier_id = False
                    if line.is_outsource:
                        if line.is_billboard_product and not line.asset_id.is_virtual_billboard:
                            supplier_id = line.asset_id.company_id.partner_id.id
                        if line.outsource_supplier_id:
                            supplier_id = line.outsource_supplier_id.id
                    order.append(
                        Command.create(
                            {
                                "product_id": line.product_id.id,
                                "qty": line.product_uom_qty,
                                "meter_square": line.meter_square,
                                "price_unit": line.price_unit,
                                "sale_line_id": line.id,
                                "subtotal": line.price_subtotal,
                                "subtotal_without_discount": line.price_subtotal_without_discounts,
                                "is_billboard_product": True if line.is_billboard_product
                                                                or line.asset_id.is_virtual_billboard else False,
                                "is_virtual_billboard": True if line.asset_id.is_virtual_billboard else False,
                                "outsource_type": "complete_so" if line.is_billboard_product else False,
                                "outsource_to": outsource_to,
                                "default_supplier_ids": self.get_outsource_default_suppliers(line=line)
                                if line.is_billboard_product else False,
                                "supplier_id": supplier_id
                            }
                        )
                    )
            return order
        else:
            return super()._prepare_order_line_outsource()

    def action_close_printing_sale(self):
        res = super().action_close_printing_sale()
        self.update_order_line_invoicing_quantity()
        return res

    def update_order_line_invoicing_quantity(self):
        for line in filter(
            lambda ln: ln.is_billboard_product and ln.asset_id, self.order_line
        ):
            contract_line = self.env["billboard.asset.rc.line"].sudo().search(
                [
                    ("billboard_asset_id", "=", line.asset_id.id),
                    ("sale_order_id", "=", self.id),
                    ("sale_line_id", "=", line.id),
                ], limit=1
            )
            if contract_line:
                line.sudo().qty_invoiced = line.sudo().product_uom_qty
                line.sudo().qty_to_invoice = 0.0
                line.sudo().task_completed = True

    def action_confirm(self):
        res = super().action_confirm()
        if self.order_line:
            for line in filter(lambda line: line.asset_id, self.order_line):
                self.validate_billboard_date(line=line)
            billboard_lines = filter(
                lambda line: line.product_id.billboard_product, self.order_line
            )
            if any(not line.asset_id for line in billboard_lines):
                raise UserError(_("Please Fill the actual billboard in order line"))
            for line in self.order_line:
                line.is_confirmed = True
            if self.billboard_contract_ids:
                if all(contract.contract_stage not in ("running", "cancelled", "terminated") for contract in
                       self.billboard_contract_ids):
                    raise UserError(_("Please confirm all the contracts to confirm the Order"))
            self.sudo().update_order_line_invoicing_quantity()
        return res


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    billboard_product = fields.Boolean()
    billboard_renting_asset_ids = fields.Many2many(
        "billboard.asset.rc.line",
        "sale_billboard_renting_asset_rel",
        string="Renting Asset",
    )
    billboard_renting_contract_line_ids = fields.Many2many(
        "billboard.asset.rc.line",
        "billboard_renting_contract_line_rel",
        string="Renting Contract Lines",
    )
    asset_id = fields.Many2one("account.asset", string="Billboard Asset")
    billboard_ids = fields.Many2many('account.asset', string="Billboards")
    from_date = fields.Date(string="From Date")
    to_date = fields.Date(string="To Date")
    no_of_unit = fields.Integer(string="No of Unit")
    rental_frequency = fields.Selection(
        [
            ("daily", "Daily"),
            ("monthly", "Monthly"),
            ("year", "Yearly"),
        ]
    )
    billboard_type = fields.Selection(
        [
            ("static", "Static"),
            ("dynamic", "Dynamic")
        ],
        string="Billboard Rent")
    billboard_type_id = fields.Many2one("billboard.type.master", "Billboard Type")
    billboard_size_id = fields.Many2one("billboard.size.master", "Billboard Size")
    billboard_street_id = fields.Many2one("res.street", string="Billboard Street")
    billboard_district_id = fields.Many2one("res.som.district", string="Billboard District")
    billboard_order_type = fields.Selection(
        [
            ("quotation", "Quotation"),
            ("order", "Sale Order"),
        ],
        string="Billboard Order Type"
    )
    is_confirmed = fields.Boolean(default=False)
    product_for_billboard_line_id = fields.Many2one("account.asset", string="Product For Billboard Line")
    printing_for_billboard_line_id = fields.Many2one("sale.order.line", string="Printing For Billboard Line")
    billboard_slot_ids = fields.Many2many("billboard.slot.line", string="Billboard Slots")
    is_billboard_product = fields.Boolean(default=False)
    renting_contract_created = fields.Boolean(default=False)
    back_date_contract_reason = fields.Char(string="Back Date Contract Reason")

    @api.onchange("product_id")
    def onchange_product(self):
        res = super().onchange_product()
        tmpl_id = self.product_id.product_tmpl_id
        if tmpl_id.billboard_product:
            self.is_billboard_product = True
        return res

    @api.constrains("product_id")
    def get_order_line_billboard_product(self):
        for line in self:
            tmpl_id = line.product_id.product_tmpl_id
            if tmpl_id.billboard_product:
                line.is_billboard_product = True

    def prepare_default_asset_line(self):
        line_lst = []
        default_asset_ids = self.env["account.asset"].sudo().search(
            [
                ('is_billboard', '=', True),
                ('is_virtual_billboard', '!=', True)
            ]
        )
        if default_asset_ids:
            for asset in default_asset_ids:
                line_lst.append(
                    Command.create(
                        {
                            'billboard_type_id': asset.billboard_type_id.id,
                            'size_id': asset.size_id.id,
                            'street_id': asset.street_id.id,
                            'district_id': asset.district_id.id,
                            'billboard_tag': asset.billboard_tag,
                            'asset_id': asset.rental_frequency,
                        }
                    )
                )
        return line_lst

    @api.onchange('billboard_product')
    def onchange_open_billboard_wz(self):
        if self.billboard_product:
            return {
                'type': 'ir.actions.act_window',
                'name': 'Billboard Rent',
                'res_model': 'sale.billboard.rent.wz',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_sale_line_id': self.id,
                    'default_is_so_confirmed': True if self.order_id.printing_sale_status
                                                       in ("sale", "completed", "done") else False,
                }
            }

    @api.onchange("product_id", "billboard_slot_ids")
    def onchange_product(self):
        res = super().onchange_product()
        self.billboard_product = False
        if self.product_id and self.product_id.billboard_product:
            self.billboard_product = self.product_id.billboard_product
            self.get_billboard_slot_price()
        return res

    def get_billboard_slot_price(self):
        if self.billboard_slot_ids:
            self.price_unit = 0.0
            for slot in self.billboard_slot_ids:
                self.price_unit += slot.price

    @api.onchange('product_uom')
    def product_uom_change(self):
        res = super().product_uom_change()
        if self.product_id and self.billboard_slot_ids:
            self.get_billboard_slot_price()
        return res

    @api.onchange('product_id', 'price_unit', 'product_uom', 'product_component_ids', 'from_date', 'to_date',
                  'product_uom_qty', 'tax_id', 'height', 'width', 'pages', 'order_id')
    def _onchange_discount(self):
        res = super()._onchange_discount()
        if (self.is_billboard_product and self.from_date and self.to_date
                and self.asset_id and self.asset_id.billboard_tag):
            try:
                from_date = datetime.strptime(str(self.from_date), '%Y-%m-%d')
                to_date = datetime.strptime(str(self.to_date), '%Y-%m-%d')
                months = []
                current_date = from_date
                while current_date <= to_date:
                    month_name = current_date.strftime('%B')
                    if month_name not in months:
                        months.append(month_name)
                    current_date += relativedelta(months=1)
                months_str = ", ".join(months)
                self.name = f"{self.product_id.name} ({str(self.asset_id.billboard_tag)}, {months_str})"
            except ValueError:
                self.name = f"{self.product_id.name} ({str(self.asset_id.billboard_tag)}, Invalid Date)"
        return res

    def check_billboard_printing_product(self, asset_id):
        if asset_id:
            for line in self.order_id.order_line:
                if line.product_id and not line.charged_for_sale_line_id:
                    if (line.product_for_billboard_line_id
                            and line.printing_for_billboard_line_id):
                        if line.product_for_billboard_line_id == asset_id:
                            return line

    def get_default_selected_product_component_ids(self, line):
        selected_product_component_ids = False
        for line in self:
            printing_product = self.env["sale.order.line"].sudo().search(
                [("printing_for_billboard_line_id", "=", line.id),
                 ("charged_for_sale_line_id", "=", False),
                 ("order_id", "=", line.order_id.id),
                 ("product_for_billboard_line_id", "=", line.asset_id.id)], limit=1
            )
            if printing_product:
                product_component_obj = self.env["product.component.line"]
                product_comp_ids = product_component_obj.search(
                    [("template_id", "=", printing_product.product_id.product_tmpl_id.id)]
                )
                product_component_ids = product_comp_ids.mapped("product_component_id")
                if printing_product.product_id:
                    selected_product_component_ids = [(6, 0, product_component_ids.ids)]
                if not printing_product.product_id:
                    selected_product_component_ids = False
        return selected_product_component_ids

    def prepare_asset_added_lines(self):
        line_list = []
        for line in filter(
            lambda ln: (ln.asset_id and not ln.is_confirmed
                         and ln.is_billboard_product), self.order_id.order_line
        ):
            sale_line_id = self.check_billboard_printing_product(asset_id=line.asset_id)
            selected_product_component_ids = self.get_default_selected_product_component_ids(line=line)
            line_list.append(
                Command.create(
                    {
                        'billboard_type_id': line.billboard_type_id.id,
                        'size_id': line.billboard_size_id.id,
                        'street_id': line.billboard_street_id.id,
                        'district_id': line.billboard_district_id.id,
                        'billboard_tag': line.asset_id.billboard_tag,
                        'asset_id': line.asset_id.id,
                        'frequency_type': line.rental_frequency,
                        'start_date': line.from_date,
                        'to_date': line.to_date,
                        'no_of_unit': line.product_uom_qty,
                        'sale_line_id': line.id,
                        'slot_unit_prices': line.price_unit,
                        'total_slot_prices': line.product_uom_qty * line.price_unit,
                        'billboard_slot_ids': line.billboard_slot_ids.ids,
                        'printing_required': 'yes' if sale_line_id else 'no',
                        'product_id': sale_line_id.product_id.id
                        if sale_line_id else False,
                        'product_component_ids': sale_line_id.product_component_ids.ids
                        if sale_line_id else False,
                        'selected_product_component_ids': selected_product_component_ids
                        if selected_product_component_ids else False,
                        'designing_required': sale_line_id.design_required
                        if sale_line_id else False,
                        'installation_required': sale_line_id.installation_required
                        if sale_line_id else False,
                        'is_so_confirmed': True if line.order_id.printing_sale_status
                                                   in ("sale", "completed") else False
                    }
                )
            )
        return line_list

    def get_payment_term_line_ids(self):
        contract_id = self.env["billboard.renting.contract"].sudo().search(
            [
                ("sale_id", "=", self.order_id.id),
                ("contract_stage", "=", "draft"),
            ], limit=1
        )
        payment_term_list = []
        if contract_id:
            for line in contract_id.billboard_contract_payment_term_line:
                term_id = Command.create(
                    {
                        "payment_date": line.payment_date,
                        "payment_amount": line.payment_amount,
                    }
                )
                payment_term_list.append(term_id)
            return payment_term_list
        return False

    def action_open_billboard_wz(self):
        # default_asset_ids = self.prepare_default_asset_line()
        added_lines = self.prepare_asset_added_lines()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Billboard Rent',
            'res_model': 'sale.billboard.rent.wz',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_line_id': self.id,
                'default_billboard_add_lines': added_lines,
                'default_billboard_order_type': 'order',
                'default_is_new_asset': True if self.asset_id else False,
                'default_is_so_confirmed': True if self.order_id.printing_sale_status
                                            in ("sale", "completed", "done") else False,
                'default_payment_term_line_ids': self.get_payment_term_line_ids(),
            }
        }
