# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import _, api, fields, models
from odoo.exceptions import UserError
from datetime import date, datetime, timedelta


class AssetBillboard(models.Model):
    _inherit = "account.asset"
    _order = "id desc, create_date desc"

    name = fields.Char(
        string="Asset Name",
        compute="_compute_name",
        store=True,
        required=False,
        readonly=False,
    )
    stage = fields.Selection(
        [
            ("available", "Available"),
            ("booked", "Booked"),
            ("rented", "Rented"),
            ("damaged", "Damaged"),
        ],
        tracking=True,
        default="available",
    )
    billboard_tag = fields.Char(
        required=False,
        copy=False,
        readonly=False,
        tracking=True,
        string="Billboard Tag",
        compute="_compute_billboard_tag",
        store=True
    )
    new_billboard_tag = fields.Char(string="Billboard Tag")
    rc_line = fields.One2many("billboard.renting.contract.line", "asset_id")
    rc_partner_id = fields.Many2one(
        "res.partner", tracking=True, compute="_compute_running_rc_customer"
    )
    rc_ids = fields.Many2many("billboard.renting.contract", tracking=True)
    model_id = fields.Many2one("account.asset")
    inspection_line = fields.One2many(
        "billboard.inspection", "billboard_id", tracking=True
    )
    billboard_type_id = fields.Many2one(
        "billboard.type.master", string="Billboard Type", tracking=True
    )
    country_id = fields.Many2one("res.country", tracking=True)
    state_id = fields.Many2one("res.country.state", tracking=True)
    city_id = fields.Many2one("res.city", tracking=True)
    district_id = fields.Many2one("res.som.district", tracking=True)
    region_id = fields.Many2one("res.som.region", tracking=True)
    street_id = fields.Many2one("res.street", tracking=True)
    area = fields.Text("Area", tracking=True)
    picture_ids = fields.Many2many("ir.attachment", string="Picture", tracking=True)
    rental_price = fields.Float("Rental Price", tracking=True)
    size_id = fields.Many2one("billboard.size.master", string="Size", tracking=True)
    longitude = fields.Float("Longitude", tracking=True)
    latitude = fields.Float("Latitude", tracking=True)
    installed_date = fields.Date("Installed Date", tracking=True)
    is_billboard = fields.Boolean()
    is_billboard_form = fields.Boolean()
    invoice_line = fields.One2many("account.move", "billboard_id", tracking=True)
    branch_id = fields.Many2one(
        "res.branch", tracking=True, default=lambda self: self.env.user.branch_id.id
    )
    total_equipment_request = fields.Integer(compute="_compute_total_equipment_request")
    is_virtual_billboard = fields.Boolean()
    is_rental_price_editable = fields.Boolean(tracking=True)
    is_sale_rent_select = fields.Boolean()
    billboard_product_ids = fields.Many2many("product.product", string="Products")
    visibility_a = fields.Char(string="Visibility A")
    visibility_b = fields.Char(string="Visibility B")
    billboard_slot_line = fields.One2many(
        "billboard.slot.line",
        "billboard_slot_id",
        string="Billboard Slot Line"
    )
    billboard_slot_line_ids = fields.Many2many(
        "billboard.slot.line",
        "billboard_slot_ids_rel",
        string="Billboard Slot Line"
    )
    is_billboard_slots_available = fields.Boolean(
        compute="compute_billboard_slot_available",
        string="Billboard Slot Available",
        store=True
    )
    billboard_renting_contract_ids = fields.Many2many(
        "billboard.renting.contract",
        "asset_contract_rel",
        compute="_compute_all_running_contracts",
    )

    @api.depends("billboard_slot_line")
    def compute_billboard_slot_available(self):
        """
        Checking the billboard Status based on Slots
        """
        for billboard in self:
            billboard.is_billboard_slots_available = False
            if any(line.status == "available" for line in billboard.billboard_slot_line):
                billboard.stage = "available"
                billboard.is_billboard_slots_available = True
            elif all(line.status == "rented" for line in billboard.billboard_slot_line):
                billboard.stage = "rented"
            elif all(line.status == "booked" for line in billboard.billboard_slot_line):
                billboard.stage = "booked"
            elif all(line.status == "damaged" for line in billboard.billboard_slot_line):
                billboard.stage = "damaged"

    def action_make_available(self):
        """
        Method to make stage to available
        :return:
        """
        self.stage = "available"
        stages = ["booked", "rented", "damaged"]
        list1 = []
        for line in self.rc_line:
            if line.rc_id:
                for billboard_line in line.rc_id.billboard_asset_line:
                    if line.asset_id.id == billboard_line.billboard_asset_id.id:
                        billboard_line.stage = "available"
                        line.contract_stage = "draft"
                        billboard_line.rc_id.billboard_asset_ids = [(4, self.id)]
                        self.rc_ids = [(3, billboard_line.rc_id.id)]
                        billboard_line.rc_id.update_available_billboard_asset()
                        list1.append(billboard_line.stage)
                    list1.append(billboard_line.stage)

            result = any(elem in list1 for elem in stages)
            if result:
                line.rc_id.contract_stage = line.rc_id.contract_stage
            else:
                line.rc_id.contract_stage = "draft"
                line.rc_id.is_booked = False

    def check_and_make_available_billboard(self):
        pass

    @api.onchange('billboard_slot_line', 'billboard_slot_line.name',
                  'billboard_slot_line.show_time')
    def onchange_billboard_slot_line(self):
        if self.billboard_slot_line:
            line_records = self.billboard_slot_line
            self.billboard_slot_line_ids = [(6, 0, line_records.ids)]
        else:
            self.billboard_slot_line_ids = [(5, 0, 0)]

    @api.constrains('billboard_slot_line', 'billboard_slot_line.name',
                    'billboard_slot_line.show_time')
    def get_billboard_slot_line_ids(self):
        if self.billboard_slot_line:
            line_records = self.billboard_slot_line
            self.billboard_slot_line_ids = [(6, 0, line_records.ids)]
        else:
            self.billboard_slot_line_ids = [(5, 0, 0)]

    def create_street_sequence(self, sequence_name):
        """
        Create Sequence For street
        """
        sequence = self.env["ir.sequence"].sudo().create(
            {
                "name": sequence_name,
                "padding": 5,
                "company_id": self.company_id.id,
                "code": _("res.street.seq.%s" % str(self.id)),
            }
        )
        return sequence

    @api.model
    def _name_search(self, name, domain=None, operator='ilike', limit=None, order=None):
        domain = domain or []
        if name:
            domain = ['|', ('billboard_tag', operator, name), ('name', operator, name)] + domain
        results = self._search(domain, limit=limit, order=order)
        return results

    def _compute_all_running_contracts(self):
        self.billboard_renting_contract_ids = False
        running_contracts_line_ids = self.env["billboard.asset.rc.line"].sudo().search(
            [("billboard_asset_id", "=", self.id), ("rc_id.contract_stage", "=", "running")]
        )
        if running_contracts_line_ids:
            self.billboard_renting_contract_ids = running_contracts_line_ids.mapped("rc_id")

    def generate_street_sequence(self):
        """
        Generate Street Sequence
        """
        if self.street_id.sequence_prefix:
            number = self.street_id.sequence_id.sudo().next_by_id()
            name = self.sudo().street_id.sequence_prefix + str(number)
            self.sudo().billboard_tag = name
        elif not self.street_id.sequence_prefix:
            self.sudo().billboard_tag = self.street_id.name

    def unlink(self):
        """
        Restrict Unlinking of Billboard Asset
        """
        for rec in self:
            if rec.is_billboard:
                if rec.state in ("open", "paused", "close"):
                    if rec.stage in ("booked", "rented"):
                        raise UserError(_(f"You cannot delete the billboard in {self.stage} stage"))
        return super().unlink()

    @api.depends("rc_ids.contract_stage", "rc_ids")
    def _compute_running_rc_customer(self):
        """
        Compute Customer of current running renting contract
        """
        for asset in self:
            asset.rc_partner_id = []
            if asset.rc_ids:
                for rc in asset.rc_ids:
                    if rc.contract_stage == "running":
                        asset.rc_partner_id = rc.partner_id.id

    @api.onchange("is_virtual_billboard", "active")
    def check_active_virtual_billboards(self):
        virtual_billboards = self.env["account.asset"].sudo().search(
            [
                ("is_virtual_billboard", "=", True),
                ("active", "=", True),
                ("company_id", "=", self.company_id.id)
            ]
        )
        if virtual_billboards and len(virtual_billboards) > 1:
            raise UserError(_(
                f"There is already active virtual billboard in {self.company_id.name}"
            ))

    @api.constrains("is_virtual_billboard", "active")
    def check_and_validate_active_virtual_billboards(self):
        virtual_billboards = self.env["account.asset"].sudo().search(
            [
                ("is_virtual_billboard", "=", True),
                ("active", "=", True),
                ("company_id", "=", self.company_id.id)
            ]
        )
        if virtual_billboards and len(virtual_billboards) > 1:
            raise UserError(_(
                f"There is already active virtual billboard in {self.company_id.name}"
            ))

    def validate(self):
        """
        Validation to fill the Billboard data from billboard
        Form View if it is created from Asset Form View
        """
        res = super().validate()
        if self.is_billboard:
            if not self.is_billboard_form:
                if not self.billboard_tag:
                    raise UserError("Please fill the billboard value from billboard form view, then confirm")
        return res

    def _compute_total_equipment_request(self):
        """
        Total Maintenance Equipment Requested
        """
        for asset in self:
            asset.total_equipment_request = 0
            if asset.is_billboard:
                asset.total_equipment_request = self.env[
                    "maintenance.request"
                ].search_count([("billboard_asset_id", "=", asset.id)])

    @api.model
    def create(self, vals):
        """
        Serial Numer generate
        """
        res = super().create(vals)
        # if res.is_billboard or res.is_virtual_billboard:
        #     res.generate_street_sequence()
        if res.is_billboard and not res.is_virtual_billboard:
            res.asset_type = "purchase"
            if res.is_billboard and res.is_billboard_form:
                res.first_depreciation_date = res.installed_date
            res._onchange_model_id()
            res.compute_depreciation_board()
        return res

    # @api.constrains("street_id")
    # def onchange_street(self):
    #     """
    #     Sequence based on street
    #     """
    #     if self.street_id:
    #         self.generate_street_sequence()

    @api.onchange(
        "country_id", "state_id", "city_id", "district_id", "region_id", "street_id"
    )
    def onchange_address(self):
        """Domain Address"""
        address = {}
        if self.country_id:
            address["state_id"] = [("country_id", "=", self.country_id.id)]
        if self.state_id:
            address["region_id"] = [
                ("country_id", "=", self.country_id.id),
                ("state_id", "=", self.state_id.id),
            ]
        if self.region_id:
            self.country_id = self.region_id.country_id
            self.state_id = self.region_id.state_id
            # address['district_id'] = [('region_id', '=', self.region_id.id)]
        # if self.district_id:
        #     self.country_id = self.district_id.country_id
        #     self.state_id = self.district_id.state_id
        #     self.region_id = self.district_id.region_id
        #     address['city_id'] = [('district_id', '=', self.district_id.id)]
        if self.city_id:
            self.country_id = self.city_id.country_id
            self.state_id = self.city_id.state_id
            self.region_id = self.city_id.region_id
            # self.district_id = self.city_id.district_id
        if self.street_id:
            self.city_id = self.street_id.city_id
            self.country_id = self.street_id.country_id
            self.state_id = self.street_id.state_id
            self.region_id = self.street_id.region_id
            # self.district_id = self.street_id.district_id
        return {"domain": address}

    @api.depends("original_move_line_ids",
                 "is_billboard", "is_virtual_billboard")
    def _compute_name(self):
        for asset in self:
            if asset.is_billboard or asset.is_virtual_billboard:
                if self.street_id.sequence_prefix:
                    number = self.street_id.sequence_id.sudo().next_by_id()
                    name = self.sudo().street_id.sequence_prefix + str(number)
                    self.name = name
                elif not self.street_id.sequence_prefix:
                    self.billboard_tag = self.street_id.name
            else:
                asset.name = asset.name or (
                        asset.original_move_line_ids
                        and asset.original_move_line_ids[0].name
                        or ""
                )

    @api.depends("new_billboard_tag")
    def _compute_billboard_tag(self):
        for billboard in self:
            billboard.billboard_tag = billboard.new_billboard_tag

    # @api.constrains("rental_price")
    # def validate_rental_price(self):
    #     """
    #     Price cannot be negative
    #     """
    #     if self.is_billboard:
    #         if self.rental_price < 0:
    #             raise UserError(_("Rental Price cannot be negative!"))
    #         if self.rental_price == 0.0:
    #             raise UserError(_("Rental Price cannot be zero!"))

    # ====================================== Common Methods ======================================
    def prepare_action_maintenance_equipment_request(self):
        """
        Prepare action for Maintenance Equipment Request
        """
        action = {
            "name": _("Billboard Maintenance Request"),
            "view_mode": "form",
            "res_model": "maintenance.request",
            "target": "current",
            "type": "ir.actions.act_window",
        }
        return action

    # ====================================== Smart Button Methods ======================================
    def action_equipment_request(self):
        """
        Maintenance Equipment requested by the billboard asset
        """
        action = self.prepare_action_maintenance_equipment_request()
        action["domain"] = [
            ("is_equipment_request", "=", True),
            ("is_fleet_maintenance_request", "=", False),
            ("billboard_asset_id", "=", self.id),
        ]
        action["context"] = {
            "default_is_equipment_request": True,
            "default_is_fleet_maintenance_request": False,
            "default_billboard_asset_id": self.id,
            "create": False,
        }
        action.update({"view_mode": "tree,form"})
        return action

    # ====================================== Button Methods ======================================
    def action_damaged(self):
        """
        Asset is Damaged
        """
        if self.is_billboard:
            if self.stage == "damaged":
                raise UserError(_("Please refresh your page!"))
            self.billboard_slot_line.update({"status": "damaged"})
            self.stage = "damaged"
            running_contracts = []
            for slot in self.billboard_slot_line:
                running_contract_lines = self.env["billboard.asset.rc.line"].sudo().search([
                    ("billboard_asset_id", "=", self.id),
                    ("billboard_contract_slot_ids", "in", slot.id),
                    ("rc_id.company_id", "=", self.company_id.id),
                    # ("rc_id.contract_stage", "not in", ("cancelled", "terminated")),
                ])
                for contract_line in running_contract_lines:
                    if contract_line.rc_id not in running_contracts:
                        running_contracts.append(contract_line.rc_id)
            if running_contracts:
                for contract in running_contracts:
                    contract.update({"contract_stage": "cancelled"})
                    contract.billboard_asset_line.update({"stage": "damaged"})

    def action_repaired(self):
        """
        Asset is Repaired
        """
        if self.is_billboard:
            if self.stage != "damaged":
                raise UserError(_("Please refresh your page!"))
            self.billboard_slot_line.update({"status": "available"})
            self.stage = "available"
            related_contracts = []
            for slot in self.billboard_slot_line:
                related_contract_lines = self.env["billboard.asset.rc.line"].sudo().search([
                    ("billboard_asset_id", "=", self.id),
                    ("billboard_contract_slot_ids", "in", slot.id),
                    ("rc_id.company_id", "=", self.company_id.id),
                    ("stage", "=", "damaged"),
                    # ("rc_id.contract_stage", "in", ("cancelled", "terminated")),
                ])
                for contract_line in related_contract_lines:
                    if contract_line.rc_id not in related_contracts:
                        related_contracts.append(contract_line.rc_id)
            if related_contracts:
                for contract in related_contracts:
                    contract.update({"contract_stage": "draft"})
                    contract.billboard_asset_line.update({"stage": "available"})
            for line in self.billboard_slot_line:
                line.status = "available"

    def action_create_maintenance_equipment(self):
        """
        Asset Maintenance Equipment Request
        """
        action = self.prepare_action_maintenance_equipment_request()
        action["context"] = {
            "default_is_equipment_request": True,
            "default_is_fleet_maintenance_request": False,
            "default_billboard_asset_id": self.id,
        }
        return action


class RentingContractLine(models.Model):
    _name = "billboard.renting.contract.line"
    _description = "Billboard Renting Contract Line"
    _order = "id desc"

    asset_id = fields.Many2one("account.asset")
    rc_id = fields.Many2one("billboard.renting.contract")
    serial_no = fields.Char()
    name = fields.Char()
    partner_id = fields.Many2one("res.partner")
    start_date = fields.Date()
    end_date = fields.Date()
    contract_stage = fields.Selection(
        [
            ("draft", "Draft"),
            ("booked", "Booked"),
            ("submitted_customer_approval", "Submitted For Customer Approval"),
            ("customer_signed", "Customer Signed"),
            ("running", "Running"),
            ("on_hold", "On Hold"),
            ("cancelled", "Cancelled"),
            ("terminated", "Terminated"),
        ],
        tracking=True,
        default="draft",
    )
    billboard_asset_remark = fields.Char()


class BillboardSlotLine(models.Model):
    _name = "billboard.slot.line"

    billboard_slot_id = fields.Many2one("account.asset")
    name = fields.Char(string="Name")
    slot_type = fields.Selection(
        [
            ("fixed", "Fixed Slot"),
            ("time", "Time Slot"),
        ], string="Slot Type", default="fixed"
    )
    show_time = fields.Float(string="Show Time")
    price = fields.Float(string="Price")
    daily_price = fields.Float(string="Daily Price")
    monthly_price = fields.Float(string="Monthly Price")
    status = fields.Selection(
        [
            ("available", "Available"),
            ("booked", "Booked"),
            ("rented", "Rented"),
            ("damaged", "Damaged"),
        ], string="Status", default="available"
    )
    sale_id = fields.Many2one("sale.order", compute="compute_rented_sale_order")

    @api.constrains("name", "slot_type", "show_time",
                    "daily_price", "monthly_price")
    def validate_slot_line(self):
        for line in self:
            if line.name:
                if not line.slot_type:
                    raise UserError(_("Please Select Slot Type to Continue"))
                if line.show_time < 0.0:
                    raise UserError(_("Show Time cannot be less than 0.0"))
                if line.slot_type == "time" and line.show_time <= 0.0:
                    raise UserError(_("Show time should be greater than 0.0"))
                if line.daily_price <= 0.0 or line.monthly_price <= 0.0:
                    raise UserError(_("Price should be greater than 0.0"))

    @api.onchange("slot_type")
    def onchange_slot_type(self):
        for slot in self:
            if slot.slot_type == "fixed":
                slot.show_time = 0.0

    def check_and_make_available_billboard_slots(self):
        """
        Scheduler to make available the billboard
        """
        billboard_slot_ids = self.search([])
        if billboard_slot_ids:
            for slot in billboard_slot_ids:
                running_contract_line_ids = self.env["billboard.asset.rc.line"].search(
                    [
                        ("end_date", ">=", fields.Date.context_today(self)),
                        ("billboard_contract_slot_ids", "=", slot.id),
                        ("rc_id.contract_stage", "not in", ("on_hold", "terminated", "cancelled"))
                    ]
                )
                if not running_contract_line_ids:
                    slot.status = "available"
                    if any(slot.status == "available" for slot in slot.billboard_slot_id.billboard_slot_line):
                        slot.billboard_slot_id.stage = "available"
                if running_contract_line_ids:
                    slot.status = "rented"
                    if all(slot.status == "rented" for slot in slot.billboard_slot_id.billboard_slot_line):
                        slot.billboard_slot_id.stage = "rented"

    def compute_rented_sale_order(self):
        for line in self:
            line.sale_id = False
            sale_line_id = self.env["sale.order.line"].sudo().search(
                [
                    ("asset_id", "=", line.billboard_slot_id.id),
                    ("billboard_slot_ids", "in", line.id),
                    ("order_id.printing_sale_status", "!=", "cancel"),
                    ("to_date", ">=", fields.Date.today())
                ], limit=1
            )
            if sale_line_id and sale_line_id.order_id.billboard_contract_ids:
                if not all(contract.contract_stage in ("on_hold", "cancelled", "terminated")
                           for contract in sale_line_id.order_id.billboard_contract_ids):
                    line.sale_id = sale_line_id.order_id


class EquipmentRequest(models.Model):
    _inherit = "maintenance.request"

    billboard_asset_id = fields.Many2one("account.asset")
