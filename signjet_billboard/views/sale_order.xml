<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Form View to Modify it -->
    <record id="sale_order_form_view_inherit_billboard" model="ir.ui.view">
        <field name="name">sale.order</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_process.view_printing_sale_order_form_views"/>
        <field name="arch" type="xml">
            <field name="printing_sale" position="after">
                <field name="billboard_contract_ids" widget="many2many_tags" invisible="1" readonly="1"/>
                <field name="is_billboard_renting_contract_created" invisible="1" readonly="1" force_save="1"/>
                <field name="billboard_attachment_url" invisible="1" readonly="1" force_save="1"/>
                <field name="is_billboard_outsource" invisible="1" readonly="1" force_save="1"/>
            </field>
            <button name="action_delivery_tasks" position="after">
                <button name="action_view_billboard_renting_contract" string="Billboard Contracts"
                        type="object" class="oe_stat_button" icon="fa-pencil-square-o"
                        attrs="{'invisible': [('is_billboard_renting_contract_created', '=', False)]}"/>
            </button>
            <xpath expr="//field[@name='order_line']//tree//field[@name='product_id']"
                   position="after">
                <button name="action_open_billboard_wz" type="object" string="Add"
                        class="btn-primary"
                        attrs="{'invisible': ['|', ('billboard_product', '=', False), ('is_confirmed', '=', True)]}"/>
                <field name="billboard_product" invisible="1"/>
                <field name="asset_id" readonly="1" optional="hide"/>
                <field name="product_for_billboard_line_id" optional="hide" readonly="1"/>
                <field name="printing_for_billboard_line_id" optional="hide" readonly="1"/>
                <field name="billboard_type" invisible="1"/>
                <field name="billboard_type_id" invisible="1"/>
                <field name="billboard_size_id" invisible="1"/>
                <field name="billboard_renting_contract_line_ids" readonly="1" widget="many2many_tags" optional="hide"/>
                <field name="billboard_ids" readonly="1" widget="many2many_tags" invisible="1"/>
                <field name="billboard_street_id" invisible="1"/>
                <field name="billboard_district_id" invisible="1"/>
                <field name="from_date" readonly="1" invisible="1"/>
                <field name="to_date" readonly="1" invisible="1"/>
                <field name="no_of_unit" readonly="1" invisible="1"/>
                <field name="rental_frequency" invisible="1" readonly="1" force_save="1"/>
                <field name="billboard_order_type" readonly="1" invisible="1"/>
                <field name="is_confirmed" readonly="1" invisible="1" force_save="1"/>
                <field name="renting_contract_created" readonly="1" invisible="1" force_save="1"/>
                <field name="billboard_slot_ids" readonly="1" widget="many2many_tags" optional="hide"/>
                <field name="is_billboard_product" readonly="1" invisible="1" force_save="1"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='is_outsource']" position="attributes">
                <attribute name="attrs">
                    {'readonly': ['|', '|', '|',
                    ('design_product', '=', True),
                    ('installation_product', '=', True),
                    ('price_type', 'in', ('design', 'install', 'other_charge')),
                    ('is_printing_product', '=', False), ('is_billboard_product', '=', False)]}
                </attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='outsource_to']" position="attributes">
                <attribute name="attrs">
                    {'readonly': ['|', '|', '|', '|',
                    ('design_product', '=', True),
                    ('installation_product', '=', True),
                    ('is_outsource', '=', False),
                    ('price_type', 'in', ('design', 'install', 'other_charge')),
                    ('is_printing_product', '=', False), ('is_billboard_product', '=', False)]}
                </attribute>
            </xpath>
        </field>
    </record>
</odoo>

