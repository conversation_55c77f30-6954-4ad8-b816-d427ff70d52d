# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import models, fields, api, _, Command
from odoo.exceptions import ValidationError
from datetime import timedelta, datetime, time, date
from dateutil.relativedelta import relativedelta
from odoo.exceptions import UserError
from urllib.parse import urlparse


class ContractRunningWizard(models.TransientModel):
    _name = "billboard.contract.running.wiz"

    billboard_contract_id = fields.Many2one("billboard.renting.contract", string="Billboard Contract")
    contract_url = fields.Char(string="Signed Contract Attachment URL")

    @api.constrains("contract_url")
    def _check_url_format(self):
        if self.billboard_contract_id and self.contract_url:
            try:
                parsed_url = urlparse(self.contract_url)
                if not all([parsed_url.scheme, parsed_url.netloc]):
                    raise ValidationError('Invalid URL format. Please enter a valid URL.')
            except ValueError:
                raise ValidationError('Invalid URL format. Please enter a valid URL.')

    def update_billboard_slot_status(self, asset):
        if (asset and asset.billboard_asset_id
            and asset.billboard_contract_slot_ids):
            asset.update({"status": "rented"})
            asset.billboard_contract_slot_ids.update({"status": "rented"})

    def action_confirm(self):
        if self.billboard_contract_id:
            if (self.billboard_contract_id.contract_stage != "customer_signed"
                    and not self.billboard_contract_id.is_printing_sale_billboard):
                raise UserError(_("Please refresh the page to continue!"))
            self.billboard_contract_id.previous_stage = self.billboard_contract_id.contract_stage
            self.billboard_contract_id.billboard_attachment_url = self.contract_url
            self.billboard_contract_id.contract_stage = "running"
            for asset in self.billboard_contract_id.billboard_asset_line:
                if not asset.billboard_asset_id.is_virtual_billboard:
                    self.billboard_contract_id.update_billboard_rc_line(
                        billboard=asset.billboard_asset_id
                    )
                    self.update_billboard_slot_status(asset=asset)
            for asset in self.billboard_contract_id.billboard_asset_line:
                curr_invoice_line = []
                if not asset.billboard_asset_id.is_virtual_billboard:
                    if all(line.status == "booked"
                           for line in asset.billboard_asset_id.billboard_slot_line):
                        asset.billboard_asset_id.sudo().stage = "booked"
                    if all(line.status == "rented"
                           for line in asset.billboard_asset_id.billboard_slot_line):
                        asset.billboard_asset_id.sudo().stage = "rented"
                invoice_line_ids = self.billboard_contract_id.prepare_invoice_line(billboard=asset)
                curr_invoice_line.append((0, 0, invoice_line_ids))
                invoice_id = self.env["account.move"].create(
                    self.billboard_contract_id.invoice(
                        invoice_line_ids=curr_invoice_line,
                        invoice_date=fields.Date.context_today(self),
                        asset_line=asset
                    )
                )
                if invoice_id:
                    self.billboard_contract_id.renting_invoice_ids |= invoice_id
                    asset.invoice_ids |= invoice_id
                    invoice_id.action_post()
                    self.billboard_contract_id.create_payment_for_invoice(
                        invoice_id=invoice_id
                    )
                    self.billboard_contract_id.prev_invoice_generated = fields.Date.context_today(self)

