<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="sign._doc_sign" name="Document Sign">
        <div t-if="current_request_item and current_request_item.state == 'sent' and hasItems" class="o_sign_validate_banner">
            <button type="button" class="btn btn-primary o_validate_button">Validate &amp; Send Completed Document</button>
        </div>

        <div class="container-fluid">
            <button t-if="current_request_item and current_request_item.state == 'sent' and not hasItems" type="button" class="o_sign_sign_document_button btn btn-primary">Sign Document</button>
            <div class="row o_sign_page_info mobile-hide">
                <div class="col-lg-4">
                    <div class="o_sign_request_from">
                         <span t-if="portal" class="o_sign_breadcrumb"><a t-attf-href="/my/signature/{{current_request_item.id}}?{{ keep_query() }}"
                            class="o_sign_portal_link">Portal</a> / </span>
                         <span class="o_sign_request_reference_title o_sign_breadcrumb"><t t-esc="sign_request.reference"/></span>
                    </div>
                    <div class="o_sign_document_buttons">
                        <a role="button" t-if="sign_request.state == 'signed'" t-attf-href="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/completed" class="btn btn-primary o_sign_download_document_button">Download Document</a>
                        <a role="button" t-if="sign_request.state == 'signed'" t-attf-href="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/log" class="btn btn-secondary o_sign_download_log_button">Certificate</a>
                    </div>
                </div>

                <div class="col-lg-4 mobile-hide">
                    <div class="o_sign_request_from d-flex justify-content-center flex-wrap">
                        <img class="float-left mr-2 my-1" t-attf-src="#{image_data_uri(sign_request.create_uid.avatar_128)}" alt="Signature" style="max-width: 32px;"/>
                        <div>
                            <t t-if="sign_request.create_uid.name">Requested by <b><t t-esc="sign_request.create_uid.name"/></b><br/></t>
                            <t t-if="sign_request.create_uid.email"><a t-attf-href="mailto:{{sign_request.create_uid.email}}"><t t-esc="sign_request.create_uid.email"/></a><br/></t>
                        </div>
                    </div>
                </div>

                <div class="o_sign_signer_status_wrapper col-lg-4 d-flex justify-content-end flex-wrap mobile-hide">
                    <t t-if="sign_request.state != 'signed'">
                        <t t-foreach="sign_request.request_item_ids" t-as="sign">
                            <div t-if="sign.state != 'completed'" class="o_sign_signer_status clearfix pl-4 d-flex" t-att-data-id="sign.id">
                                <div class="o_sign_signer_status_info d-flex flex-column">
                                    <div class="text-left"><b><t t-esc="sign.partner_id.name if sign.partner_id else 'Public user'"/></b></div>
                                    <div class="text-left"><small><i> Waiting Signature</i><em t-if="sign.state != 'sent'"><br/>(the email access has not been sent)</em></small></div>
                                </div>
                            </div>
                        </t>
                    </t>
                    <t t-if="sign_request.nb_closed > 0">
                        <t t-foreach="sign_request.request_item_ids" t-as="sign">
                            <div t-if="sign.state == 'completed'" class="o_sign_signer_status o_sign_signer_signed clearfix pl-4 d-flex">
                                <div class="o_sign_signer_status_info d-flex flex-column">
                                </div>
                                <div class="text-left">
                                    <div class="text-left"><b><t t-esc="sign.partner_id.name if sign.partner_id else 'Public user'"/></b></div>
                                    <div class="text-left"><small><i> Signed on <t t-esc="sign.signing_date"/></i></small></div>
                                </div>
                                <img t-if="sign.signature" t-attf-src="/web/image/sign.request.item/{{sign.id}}/signature" alt="Signature"/>
                            </div>
                        </t>
                    </t>
                </div>
            </div>

            <div t-if="not hasItems and not isPDF" class="row">
                <div class="col-lg-12">
                    <a class="o_sign_image_document" t-attf-href="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/origin" target="_blank">
                        <img t-if="webimage" class="img img-fluid" t-attf-src="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/origin" alt="Signature"/>
                        <div t-if="not webimage" class="o_image" t-att-data-mimetype="sign_request.template_id.attachment_id.mimetype"/>
                    </a>
                </div>
            </div>
        </div>

        <t t-if="hasItems or isPDF">
            <t t-call="sign.items_view"/>
        </t>

        <input id="o_sign_input_sign_request_id" type="hidden" t-att-value="sign_request.id"/>
        <input id="o_sign_input_sign_request_token" type="hidden" t-att-value="sign_request.access_token"/>
        <input id="o_sign_input_sign_request_state" type="hidden" t-att-value="sign_request.state"/>
        <input id="o_sign_input_access_token" type="hidden" t-att-value="token"/>
        <input id="o_sign_signer_name_input_info" type="hidden" t-att-value="current_request_item.partner_id.name if current_request_item and current_request_item.partner_id else None"/>
        <input id="o_sign_signer_phone_input_info" type="hidden" t-att-value="current_request_item.partner_id.mobile if current_request_item and current_request_item.partner_id else None"/>
        <input id="o_sign_input_optional_redirect_url" type="hidden" t-att-value="sign_request.template_id.redirect_url"/>
        <input id="o_sign_input_optional_redirect_url_text" type="hidden" t-att-value="sign_request.template_id.redirect_url_text"/>
        <input t-if="current_request_item and current_request_item.state == 'sent'" id="o_sign_ask_location_input" type="hidden"/>
        <t t-if="len(sign_request.request_item_ids) == 1 and not sign_request.request_item_ids[0].partner_id">
            <input id="o_sign_is_public_user" type="hidden"/>
        </t>
    </template>

    <template id="sign.doc_sign" name="Document Sign">
        <t t-call="web.layout">
            <t t-if="current_request_item and current_request_item.partner_id.lang">
                <t t-set="html_data" t-value="{'lang': current_request_item.partner_id.lang.replace('_', '-')}"/>
            </t>
            <t t-set="head">
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
                <t t-call-assets="web.assets_common" t-js="false"/>
                <t t-call-assets="web.assets_frontend" t-js="false"/>
                <link rel="stylesheet" type="text/scss" href="/web_enterprise/static/src/scss/modal_mobile.scss"/>
                <script type="text/javascript">
                    odoo.__session_info__ = <t t-out="json.dumps(request.env['ir.http'].get_frontend_session_info())"/>;
                </script>
                <t t-call-assets="web.assets_common" t-css="false"/>
                <t t-call-assets="web.assets_frontend" t-css="false"/>
                <t t-call="web.conditional_assets_tests"/>

                <script type="text/javascript">
                    odoo.define("sign.document_custom_page", function (require) {
                        var ajax = require("web.ajax");
                        var core = require("web.core");
                        var document_signing = require("sign.document_signing");
                        var rootWidget = require('root.widget');
                        // YTI FIXME We need the tour to wait the Tip template to be loaded
                        odoo.__TipTemplateDef = ajax.loadXML("/web_tour/static/src/xml/tip.xml", core.qweb).then(function () {
                            ajax.loadXML("/sign/static/src/xml/sign_common.xml", core.qweb).then(function () {
                                document_signing.initDocumentToSign(rootWidget);
                            });
                        });
                    });
                </script>
            </t>
            <div class="o_sign_document">
                <header>
                    <div class="container-fluid">
                    <div class="d-flex justify-content-between flex-fill">
                        <div class="col-lg-4 justify-content-start">
                            <div class="o_logo">
                                <a href="/"><img src="/logo.png" alt="Logo"/></a>
                            </div>
                        </div>
                        <div class="col-lg-4 justify-content-center mobile-hide">
                        <div t-if="sign_request" class="o_sign_header_instruction">
                            <t t-if="not current_request_item">Need to sign? Check your inbox to get your secure access</t>
                            <t t-if="current_request_item and current_request_item.state == 'sent'">Please Review And Act On This Document</t>
                            <t t-if="current_request_item and current_request_item.state == 'completed'">You have completed the document</t>
                        </div>
                        </div>
                        <div class="col-lg-4 justify-content-end">
                        <div class="o_odoo">
                            <a href="https://www.odoo.com/app/sign?utm_source=db&amp;utm_medium=sign"><img src="/sign/static/img/odoo_signed.png" alt="Signed"/></a>
                        </div>
                        </div>
                    </div>
                    </div>
                </header>
                <t t-call="sign._doc_sign"/>
            </div>
        </t>
    </template>

    <template id="sign.encrypted_ask_password" name="PDF Encrypted Password Request">
        <t t-call="web.layout">
            <t t-set="head">
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
                <t t-call-assets="web.assets_common" t-js="false"/>
                <t t-call-assets="web.assets_frontend" t-js="false"/>
                <link rel="stylesheet" type="text/scss" href="/web_enterprise/static/src/scss/modal_mobile.scss"/>
                <t t-call-assets="web.assets_common" t-css="false"/>
                <t t-call-assets="web.assets_frontend" t-css="false"/>
            </t>
            <div class="container text-center">
                <h3>Missing Password</h3>
                <p>
                    The PDF's password is required to generate the final document.
                </p>
                <form string="PDF is encrypted" role="form" method="post" onsubmit="this.action = this.action + location.hash">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                    <div>
                        <p class="alert alert-danger mb8" t-if="error" role="alert">
                            <t t-esc="error"/>
                        </p>
                    </div>
                    <input type="password" required="required" name="password" t-att-autofocus="autofocus" maxlength="50"/>
                    <input type="submit" value="Generate Document" ></input>
                </form>
            </div>
        </t>
    </template>

    <template id="sign.deleted_sign_request" name="Missing Signature Request">
        <t t-call="web.layout">
            <t t-set="head">
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
                <t t-call-assets="web.assets_common" t-js="false"/>
                <t t-call-assets="web.assets_frontend" t-js="false"/>
                <link rel="stylesheet" type="text/scss" href="/web_enterprise/static/src/scss/modal_mobile.scss"/>
                <t t-call-assets="web.assets_common" t-css="false"/>
                <t t-call-assets="web.assets_frontend" t-css="false"/>
            </t>
            <div class="container">
                <h3>Missing signature request</h3>
                <p>
                    The signature access you are trying to reach does not exist. Maybe the signature request has been deleted or modified. <br/>
                    If there still exists a signature request for this document, check your inbox to get your access!
                </p>
            </div>
        </t>
    </template>

</odoo>
