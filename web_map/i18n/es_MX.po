# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_map
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                    Get token"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                    Obtener token"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Map Routes</span>"
msgstr "<span class=\"o_form_label\">Rutas en mapas</span>"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Vista de ventana de acción"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_renderer.js:0
#: code:addons/web_map/static/src/js/map_renderer.js:0
#, python-format
msgid "Address"
msgstr "Dirección"

#. module: web_map
#: model:ir.model,name:web_map.model_base
msgid "Base"
msgstr "Base"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: web_map
#: model:ir.model,name:web_map.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: web_map
#: model:ir.model,name:web_map.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_partner__contact_address_complete
#: model:ir.model.fields,field_description:web_map.field_res_users__contact_address_complete
msgid "Contact Address Complete"
msgstr "Dirección de contacto completa"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_view.js:0
#, python-format
msgid "Items"
msgstr "Elementos"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "Locating new addresses..."
msgstr "Localizando nuevas direcciones..."

#. module: web_map
#: model:ir.model.fields.selection,name:web_map.selection__ir_actions_act_window_view__view_mode__map
#: model:ir.model.fields.selection,name:web_map.selection__ir_ui_view__type__map
msgid "Map"
msgstr "Mapa"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_model.js:0
#, python-format
msgid "MapBox servers unreachable"
msgstr "No se puede acceder a los servidores de Mapbox"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_renderer.js:0
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: web_map
#: model:ir.model.fields,help:web_map.field_res_config_settings__map_box_token
msgid "Necessary for some functionalities in the map view"
msgstr "Necesario para algunas funciones en la vista del mapa"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Set a MapBox account to activate routes and style"
msgstr "Configure una cuenta de Mapbox para activar rutas y estilos"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "Set up token"
msgstr "Configurar token"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_model.js:0
#, python-format
msgid "Some routing points are too far apart"
msgstr "Algunos puntos de enrutamiento están demasiado separados"

#. module: web_map
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "The MapBox server is unreachable"
msgstr "No se puede acceder al servidor de Mapbox"

#. module: web_map
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "The token input is not valid"
msgstr "La entrada del token no es válida"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_model.js:0
#: code:addons/web_map/static/src/js/map_model.js:0
#: code:addons/web_map/static/src/js/map_model.js:0
#, python-format
msgid ""
"The view has switched to another provider but functionalities will be "
"limited"
msgstr ""
"Se ha cambiado la vista a otro proveedor pero las funciones serán limitadas"

#. module: web_map
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "This referer is not authorized"
msgstr "Esta referencia no está autorizada"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "To get routing on your map, you first need to setup your Mapbox token."
msgstr ""
"Primero debe configurar su token de Mapbox para poder ver la ruta en su "
"mapa."

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Token"
msgstr "Token"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_config_settings__map_box_token
msgid "Token Map Box"
msgstr "Token de Mapbox"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_model.js:0
#, python-format
msgid "Token invalid"
msgstr "Token no válido"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_model.js:0
#, python-format
msgid "Too many requests, try again in a few minutes"
msgstr "Demasiadas solicitudes, inténtelo de nuevo en unos minutos"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_model.js:0
#, python-format
msgid "Too many routing points (maximum 25)"
msgstr "Demasiados puntos de enrutamiento (máximo 25)"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_model.js:0
#, python-format
msgid "Unauthorized connection"
msgstr "Conexión no autorizada"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "Undefined"
msgstr "Indefinido"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "Unsuccessful routing request:"
msgstr "Solicitud de enrutamiento fallida:"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/js/map_view.js:0
#, python-format
msgid "Untitled"
msgstr "Sin título"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_ui_view
msgid "View"
msgstr "Ver"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_map.field_ir_ui_view__type
msgid "View Type"
msgstr "Tipo de vista"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "View in Google Maps"
msgstr "Ver en Google Maps"

#. module: web_map
#: code:addons/web_map/models/models.py:0
#, python-format
msgid "You need to set a Contact field on this model to use the Map View"
msgstr ""
"Es necesario establecer un campo de contacto en este modelo para utilizar la"
" vista de mapa"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "navigate to"
msgstr "ir a"

#. module: web_map
#. openerp-web
#: code:addons/web_map/static/src/xml/map.xml:0
#, python-format
msgid "open"
msgstr "abrir"
