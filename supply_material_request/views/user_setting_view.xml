<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="supply_internal_transfer_user_setting" model="ir.ui.view">
            <field name="name">internal.transfer.user.setting</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='preferences']" position="after">
                    <page string="Supply Management" name="supply_management">
                        <group>
                            <field name="source_trans_location" string="Physical Source Supply Location"
                                   widget="many2many_tags"
                                   domain="[('usage', '=', 'internal'), ('company_id', '=', company_id)]"
                                   options='{"no_open": True, "no_create": True}'/>
                            <field name="int_trans_loc_ids" string="Physical Handel Location" widget="many2many_tags"
                                   domain="[('usage', '=', 'internal'), ('company_id', '=', company_id)]"
                                   options='{"no_open": True, "no_create": True}'/>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
