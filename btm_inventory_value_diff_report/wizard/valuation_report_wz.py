# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd
import base64

import xlsxwriter

from odoo import fields, models, _


class ValuationDiffReportWz(models.TransientModel):
    _name = 'valuation.diff.report.call'

    company_id = fields.Many2one("res.company", string="Company", default=lambda self:self.env.company)

    def action_get_valuation_difference_report(self):
        """Get Report"""
        action = self.env.ref(
            "btm_inventory_value_diff_report.action_list_view_valuation_account_view"
        ).read()[0]
        self.env["inventory.valuation.account.value"].get_report(
            company_id=self.company_id,
        )
        return action
