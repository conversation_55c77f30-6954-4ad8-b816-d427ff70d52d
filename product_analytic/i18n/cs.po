# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_analytic
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-06-21 02:40+0000\n"
"PO-Revision-Date: 2017-06-21 02:40+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: Czech (https://www.transifex.com/oca/teams/23907/cs/)\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"

#. module: product_analytic
#: model:ir.model.fields,field_description:product_analytic.field_product_category__expense_analytic_account_id
#: model:ir.model.fields,field_description:product_analytic.field_product_product__expense_analytic_account_id
#: model:ir.model.fields,field_description:product_analytic.field_product_template__expense_analytic_account_id
msgid "Expense Analytic Account"
msgstr ""

#. module: product_analytic
#: model:ir.model.fields,field_description:product_analytic.field_product_category__income_analytic_account_id
#: model:ir.model.fields,field_description:product_analytic.field_product_product__income_analytic_account_id
#: model:ir.model.fields,field_description:product_analytic.field_product_template__income_analytic_account_id
msgid "Income Analytic Account"
msgstr ""

#. module: product_analytic
#: model:ir.model,name:product_analytic.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: product_analytic
#: model:ir.model,name:product_analytic.model_product_category
msgid "Product Category"
msgstr ""

#. module: product_analytic
#: model:ir.model,name:product_analytic.model_product_template
msgid "Product Template"
msgstr ""

#~ msgid "Invoice Line"
#~ msgstr "Řádek faktury"
