<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="sale_advance_payment_refund_view" model="ir.ui.view">
        <field name="name">sale.order</field>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <button name="action_approve_advance_payment_req"
                        type="object" string="Approve Advance Refund"
                        class="oe_highlight"
                        confirm="Are you sure you want to approve!"
                        attrs="{'invisible': ['|', ('request_advance_refund', '=', False),
                        ('approved_advance_refund', '=', True)]}"
                        />

                    <button name="action_advance_payment_refund"
                        type="object" string="Advance Payment Refund"
                        class="oe_highlight"
                        confirm="Are you sure you want to refund!"
                        attrs="{'invisible': ['|', ('approved_advance_refund', '!=', True),
                        ('is_advance_refunded', '=', True)]}"
                        />

                    <button name="action_cancel_advance_refund_req"
                        type="object" string="Reject Advance Refund"
                        class="oe_highlight"
                        confirm="Are you sure you want to Reject!"
                        attrs="{'invisible': [('request_advance_refund', '=', False)]}"
                        />
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,sent,sale"
                           context="{'default_state': 'lead'}" invisible="1"/>
                    <field name="printing_sale_status" widget="statusbar"
                           statusbar_visible="lead,sale,done" context="{'default_state': 'lead'}"/>
                </header>
                <sheet>
                    <group>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                                <field name="company_id" invisible="1"/>
                                <field name="pricelist_id" invisible="1"/>
                                <field name="approved_advance_refund" invisible="1"/>
                                <field name="request_advance_refund" invisible="1"/>
                                <field name="is_advance_refunded" invisible="1"/>
                                <field name="approved_advance_refund" invisible="1"/>
                            </h1>
                        </div>
                    </group>
                    <group colspan="4" cols="4">
                        <group>
                            <field name="partner_id" readonly="1"/>
                            <field name="amount_total" readonly="1"/>
                            <field name="adv_payment_amt" readonly="1"/>
                            <field name="refund_request_id" readonly="1"/>
                            <field name="refund_approved_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="refund_reason" readonly="1"/>
                            <field name="invoiced_amount" readonly="1"/>
<!--                        <field name="advance_refund_amt" readonly="1"/>-->
                            <field name="requested_refund_amount" readonly="1"/>
                            <field name="refund_request_type" readonly="1"/>
                            <field name="refund_reject_reason" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Order" name="order">
                            <field name="order_line" readonly="1" force_save="1">
                                <tree>
                                    <control>
                                        <create name="add_product_control" string="Add a product"/>
                                        <create name="add_section_control" string="Add a section"
                                                context="{'default_display_type': 'line_section'}"/>
                                        <create name="add_note_control" string="Add a note"
                                                context="{'default_display_type': 'line_note'}"/>
                                    </control>
                                    <field name="company_id" invisible="1"/>
                                    <field name="sequence" invisible="1"/>
                                    <!-- We do not display the type because we don't want the user to be bothered with that information if he has no section or note. -->
                                    <field name="is_price_editable" invisible="1"/>
                                    <field name="display_type" invisible="1"/>
                                    <field name="product_type" invisible="1"/>
                                    <field name="product_uom_category_id" invisible="1"/>

                                    <field name="product_updatable" invisible="1"/>
                                    <field
                                            name="product_id"
                                            attrs="{
                                            'readonly': ['|', ('product_updatable', '=', False), ('site_visit_created', '=', True)],
                                            'required': [('display_type', '=', False)],
                                        }"
                                            options="{'no_create': True, 'no_create_edit': True}"
                                            force_save="1"
                                            context="{
                                            'partner_id': parent.partner_id,
                                            'quantity': product_uom_qty,
                                            'pricelist': parent.pricelist_id,
                                            'uom':product_uom,
                                            'company_id': parent.company_id,
                                            'default_lst_price': price_unit,
                                            'default_description_sale': name,

                                            'form_view_ref': 'inventory_process.view_printing_product_form'
                                        }"
                                            domain="['|', ('is_printing_product', '=', True),('is_printing_type', '=', True), '|', ('company_id', '=', False),
                                              ('company_id', '=', parent.company_id), ('is_not_task', '=', False)]"
                                            widget="product_configurator"
                                    />
                                    <field name="printing_type" readonly="1" force_save="1" invisible="1"/>
                                    <field name="priority" string="Priority" force_save="1" invisible="1"/>
                                    <field name="sold_as" invisible="1" force_save="1"/>
                                    <field name="design_user_assigned" invisible="1" force_save="1"/>
                                    <field name="design_available" string="Designing" invisible="1"/>
                                    <field name="total_printing_price" invisible="1"/>
                                    <field name="total_installation" invisible="1"/>
                                    <field name="design_product" invisible="1"/>
                                    <field name="site_visit_created" invisible="1"/>
                                    <field name="is_inventory_product" invisible="1"/>
                                    <field name="installation_product" invisible="1"/>
                                    <field name="design_yes" invisible="1"/>
                                    <field name="design_required" attrs="{'readonly': [('design_yes', '=', False)],
                                    'required': [('design_yes', '=', True)]}"/>
                                    <field name="installation_required" force_save="1"
                                           attrs="{'readonly': ['|', ('is_inventory_product','=',True),'|', '|', ('site_visit_created', '=', True), '|', ('sold_as', '!=', 'measurement'),  ('installation_product', '=', True), '|', ('design_product', '=', True), ('price_type', '!=', False)],
                                           'required': [('sold_as', '=', 'measurement'), ('price_type', '=', False)]}"/>
                                    <field name="site_visit" invisible="1"
                                           attrs="{'required': [('printing_type', '=', 'outdoor')], 'readonly': [('printing_type', '=', 'indoor')]}"/>
                                    <field name="sample_required" invisible="1"
                                           attrs="{'readonly': [('printing_type', '=', 'indoor')]}"/>
                                    <field name="product_template_id"
                                           string="Product"
                                           invisible="1"
                                           attrs="{
                                          'readonly': [('product_updatable', '=', False)],
                                          'required': [('display_type', '=', False)],
                                      }"
                                           context="{
                                          'partner_id': parent.partner_id,
                                          'quantity': product_uom_qty,
                                          'pricelist': parent.pricelist_id,
                                          'uom':product_uom,
                                          'company_id': parent.company_id,
                                          'default_list_price': price_unit,
                                          'default_description_sale': name
                                      }"
                                           domain="[('is_printing_product', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id), ('detailed_type', '=', 'service')]"
                                           widget="product_configurator"/>
                                    <field name="name" attrs="{'readonly': [('site_visit_created', '=', True)]}"
                                           widget="section_and_note_text" optional="show"/>
                                    <field
                                            name="analytic_tag_ids"
                                            attrs="{'readonly': [('site_visit_created', '=', True)]}"
                                            optional="hide"
                                            groups="analytic.group_analytic_tags"
                                            widget="many2many_tags"
                                            options="{'color_field': 'color'}"
                                            domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                    />
                                    <field
                                            name="task_completed"
                                            readonly="1"
                                            optional="hide"
                                    />
                                    <field name="task_completed" invisible="1"/>
                                    <field string="Designing Fees" invisible="1"
                                           name="designing_fees" force_save="1"
                                           attrs="{'readonly': [('sold_as', '!=', 'measurement')], 'required': [('sold_as', '=', 'measurement')]}"
                                    />
                                    <field string="Installation Fees" invisible="1"
                                           name="installation_fees" force_save="1"
                                           attrs="{'readonly': [('sold_as', '!=', 'measurement')], 'required': [('sold_as', '=', 'measurement')]}"
                                    />
                                    <field
                                            name="height"
                                            force_save="1"
                                            attrs="{'readonly': ['|', ('sold_as', '!=', 'measurement'), ('site_visit_created', '=', True)], 'required': [('sold_as',
                                                                        '=', 'measurement')]}"
                                    />
                                    <field
                                            name="width" force_save="1"
                                            attrs="{'readonly': ['|', ('sold_as', '!=', 'measurement'), ('site_visit_created', '=', True)], 'required': [('sold_as',
                                                                        '=', 'measurement')]}"
                                    />
                                    <field string="m^2"
                                           name="meter_square" force_save="1" readonly="1"
                                    />
                                    <field
                                            name="product_uom_qty"
                                            decoration-info="(not display_type and invoice_status == 'to invoice')"
                                            decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                            context="{
                                            'partner_id': parent.partner_id,

                                            'pricelist': parent.pricelist_id,
                                            'uom': product_uom,
                                            'company_id': parent.company_id
                                        }"
                                            attrs="{'readonly': [('site_visit_created', '=', True)]}"
                                            force_save="1"
                                    />
                                    <field
                                            name="qty_delivered"
                                            decoration-info="(not display_type and invoice_status == 'to invoice')"
                                            decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                            string="Delivered"
                                            invisible="1"
                                            attrs="{
                                            'column_invisible': [('parent.state', 'not in', ['sale', 'done'])],
                                            'readonly': [('qty_delivered_method', '!=', 'manual')]
                                        }"
                                            optional="show"

                                    />
                                    <field name="qty_delivered_manual" invisible="1"/>
                                    <field name="qty_delivered_method" invisible="1"/>
                                    <field name="price_type" invisible="1"/>
                                    <field
                                            name="qty_invoiced"
                                            decoration-info="(not display_type and invoice_status == 'to invoice')"
                                            decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                            string="Invoiced"
                                            invisible="1"
                                            attrs="{'column_invisible': [('parent.state', 'not in', ['sale', 'done'])]}"
                                            optional="show"
                                    />
                                    <field name="qty_to_invoice" invisible="0"/>
                                    <field name="product_uom_readonly" invisible="1"/>
                                    <field
                                            name="product_uom"
                                            invisible="1"
                                            force_save="1"
                                            string="UoM"
                                            readonly="1"
                                            attrs="{
                                            'required': [('display_type', '=', False)],
                                        }"
                                            context="{'company_id': parent.company_id}"
                                            groups="uom.group_uom"
                                            options='{"no_open": True}'
                                            optional="show"
                                    />
                                    <field
                                            name="customer_lead"
                                            optional="hide"
                                            attrs="{'readonly': [('parent.state', 'not in', ['draft', 'sent', 'sale'])]}"
                                    />
                                    <field name="product_packaging_qty"
                                           attrs="{'invisible': ['|', ('product_id', '=', False), ('product_packaging_id', '=', False)]}"
                                           groups="product.group_stock_packaging" optional="show"/>
                                    <field name="product_packaging_id"
                                           attrs="{'invisible': [('product_id', '=', False)]}"
                                           context="{'default_product_id': product_id, 'tree_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}"
                                           groups="product.group_stock_packaging" optional="show"/>
                                    <field
                                            name="price_unit"
                                            string="Price"
                                            attrs="{'readonly': ['|', ('qty_invoiced', '&gt;', 0), '|',
                                            ('site_visit_created', '=', True), ('is_price_editable', '=', False)]}"
                                            force_save="1"
                                    />
                                    <field name="discount" invisible="1" string="Disc.%"
                                           groups="product.group_discount_per_so_line"
                                           optional="show" widget="product_discount"/>
                                    <field name="price_subtotal" widget="monetary"
                                           groups="account.group_show_line_subtotals_tax_excluded"
                                           readonly="1" force_save="1"/>
                                    <field name="price_total" widget="monetary"
                                           groups="account.group_show_line_subtotals_tax_included"/>
                                    <field name="state" invisible="1"/>
                                    <field name="invoice_status" invisible="1"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="price_tax" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                    <field name="is_design_task" invisible="1"/>
                                </tree>
                                <div class="container">
                                    <field name="enable_discount" invisible="1"/>
                                    <label for="global_discount_type" string="Sale Discount Type:"
                                           attrs="{'invisible':[('enable_discount','!=',True),'|',('amount_discount','=',0),('printing_sale_status','=','loss')]}"/>
                                    <field name="global_discount_type" class="oe_inline"
                                           attrs="{'invisible':[('enable_discount','!=',True),('amount_discount','=',0)],
                                                   'readonly':['|', ('printing_sale_status', 'in', ('sale','completed','done','cancel','loss')),
                                                   ('enable_discount','!=',True),('amount_discount','!=',0)]}"/>
                                </div>
                                <div class="container">
                                    <label for="global_discount_rate" string="Sale Discount:"
                                           attrs="{'invisible':[('enable_discount','!=',True),('amount_discount','=',0)],'readonly':[('printing_sale_status','=','loss')]}"/>
                                    <field name="global_discount_rate" class="oe_inline"
                                           attrs="{'invisible':[('enable_discount','!=',True),('amount_discount','=',0)],
                                                   'readonly':['|', ('printing_sale_status', 'in', ('sale','completed','done','cancel','loss')),
                                                   ('enable_discount','!=',True),('amount_discount','!=',0)
                                                   ]}"/>
                                </div>
                                <group class="oe_subtotal_footer oe_right" colspan="2" name="sale_total">
                                    <field name="total_ps_amount" string="Subtotal Amount"
                                           attrs="{'invisible':[('enable_discount','!=',True),('amount_discount','=',0)]}"/>
                                    <field name="amount_discount" string="Sale Discount"
                                           attrs="{'invisible':[('enable_discount','!=',True),('amount_discount','=',0)]}"/>

                                    <field name="tax_totals_json" widget="account-tax-totals-field" nolabel="1"
                                           colspan="2"/>

                                    <field name="adv_payment_amt"
                                           attrs="{'invisible': [('printing_sale_status', 'in', ['lead', 'draft', 'qualified_lead'])]}"
                                           colspan="2" readonly="1"/>

                                    <field name="total_so_balance"
                                           attrs="{'invisible': [('printing_sale_status', 'in', ['lead', 'draft', 'qualified_lead'])]}"
                                           colspan="2" string="Remaining Balance"/>
                                </group>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="sale_advance_refund_tree" model="ir.ui.view">
        <field name="name">sale.order</field>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <tree>
                <field name="date_order" widget="date"/>
                <field name="name" string="Order#"/>
                <field name="partner_id"/>
                <field name="amount_total"/>
                <field name="adv_payment_amt" string="Advance"/>
                <field name="total_so_balance" string="SO Balance"/>
                <field name="amount_invoiced" string="Invoiced Amount" invisible="1"/>
                <field name="invoiced_amount" string="Invoiced Amount"/>
                <field name="requested_refund_amount"/>
<!--                <field name="total_refunded_amount"/>-->
                <field name="latest_request_refunded_amt" string="Refunded Amount"/>
                <field name="printing_order_task_status" string="Task Status"/>
                <field name="printing_sale_status" string="Status" widget="badge"/>
                <field name="designing_status" optional="hide"/>
                <field name="designer_assignee" optional="hide"/>
                <field name="designer_deadline" string="Design Deadline" optional="hide"/>
                <field name="production_status" optional="hide"/>
                <field name="production_assignee" optional="hide"/>
                <field name="production_deadline" string="Production Deadline" optional="hide"/>
                <field name="delivery_status" optional="hide"/>
                <field name="delivery_assignee" optional="hide"/>
                <field name="delivery_deadline" string="Delivery Deadline" optional="hide"/>
                <field name="state" widget="badge" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="sale_advance_refund_action" model="ir.actions.act_window">
        <field name="name">Sale</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_advance_refund', '=', True),
            ('approved_advance_refund', '=', False),
            ('refund_request_type', '!=', False)]</field>
        <field name="context">{'create': False}</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('sale_advance_refund_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('sale_advance_payment_refund_view')})]"/>
    </record>

    <!-- This Menu Item must have a parent and an action -->
    <menuitem id="sale_adv_refund_manu"
              name="Waiting Advance Refund Approval"
              groups="refund_advance_sale_pay.advance_refund_approval_group"
              parent="common_approval.all_common_approval_main_menu"
              action="sale_advance_refund_action" sequence="23"/>

    <record id="sale_advance_refund_action_account" model="ir.actions.act_window">
        <field name="name">Sale</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('is_advance_refunded', '=', False),
            ('approved_advance_refund', '=', True)]</field>
        <field name="context">{'create': False}</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('sale_advance_refund_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('sale_advance_payment_refund_view')})]"/>
    </record>

    <menuitem id="account_sale_adv_refund_manu"
              name="SO Refund Request"
              groups="refund_advance_sale_pay.advance_refund_sale_refund_cashier"
              parent="account.menu_finance_entries_accounting_miscellaneous"
              action="sale_advance_refund_action_account" sequence="32"/>

    <menuitem id="expense_sale_adv_refund_manu"
              name="SO Refund Request"
              groups="refund_advance_sale_pay.advance_refund_sale_refund_cashier"
              parent="hr_expense.menu_hr_expense_accountant"
              action="sale_advance_refund_action_account" sequence="32"/>
</odoo>
