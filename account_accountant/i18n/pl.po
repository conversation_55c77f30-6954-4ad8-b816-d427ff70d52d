# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON><PERSON><PERSON> <s<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <judyta.kaz<PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <mlynar<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>be <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Rafał Kozak <<EMAIL>>, 2021\n"
"Language-Team: Polish (https://www.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Uzgodnienie"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Wartości tutaj wyświetlone są"
" typowe dla danej firmy.\" role=\"img\" aria-label=\"Wartości tutaj "
"wyświetlone są typowe dla danej firmy.\" "
"groups=\"base.group_multi_company\"/>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Fiscal Year</span>"
msgstr "<span class=\"o_form_label\">Rok podatkowy</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Dobra robota!</b> Przeszedłeś przez wszystkie etapy tej "
"wycieczki.</strong>"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Uzgodnienie musi zawierać 2 pozycje."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_account
#, python-format
msgid "Account"
msgstr "Konto"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Grupy kont"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_account_predictive_bills
msgid "Account Predictive Bills"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Tagi kont"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "Księgowość"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Dodaj nowy znacznik"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Wszystkie faktury i płatności zostały powiązane, salda kont zostały "
"wyrównane."

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Amount"
msgstr "Kwota"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "Księgowość analityczna"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Księgowość anglosaska"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__attachment_ids
msgid "Attachments"
msgstr "Załączniki"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Uzgodnienie wyciągu"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
msgid "Bank Statement"
msgstr "Wyciąg Bankowy"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Cancel"
msgstr "Anuluj"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Check & validate the bill. If no vendor has been found, add one before "
"validating."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Check them"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Wybierz odpowiednik lub Utwórz odpis"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "Zamknij"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr "Zamknij raport"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
msgid "Company"
msgstr "Firma"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Gratulacje, wszystko skończyłeś!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Create Reconciliation Model"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "Utwórz model"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Description"
msgstr "Opis"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Podsumowanie"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: account_accountant
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Nie masz dostępu, pomiń te dane dla wiadomości e-mail użytkownika"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr "Termin"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "Data końcowa"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "Link zewnętrzny"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
msgid "Fiscal Year"
msgstr "Rok podatkowy"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Rok podatkowy 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Lata podatkowe"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Ostatni dzień roku fiskalnego"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Ostatni miesiąc roku fiskalnego"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Get back to the dashboard using your previous path…"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "Przejdź do wyciągów bankowych"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "Dobra robota!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Great! Let’s continue."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr "Określenie konta oraz dziennika jes wymagane aby utworzyć odpis."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Pozycje"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_journal
#, python-format
msgid "Journal"
msgstr "Dziennik"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
msgid "Journal Entry"
msgstr "Zapis dziennika"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
msgid "Journal Item"
msgstr "Pozycja zapisu"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Journal Items"
msgstr "Pozycje zapisów"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Pozycje zapisów do uzgodnienia"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Label"
msgstr "Etykieta"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Ostatni dzień"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date____last_update
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "Ostatnie uzgodnienie:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Powróćmy do konsoli"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s reconcile the fetched bank transactions."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Let’s use AI to fill in the form<br/><br/><i>Tip: If the OCR is not done "
"yet, wait a few more seconds and try again.</i>"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "Wczytaj więcej... ("

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Data blokowania dla wszystkich użytkowników"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Data blokady dla osób nieposiadających uprawnień księgowania"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Data blokady"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"Match statement with existing lines on receivable/payable accounts<br>* "
"Black line: existing journal entry that should be matched<br>* Blue lines: "
"existing payment that should be matched"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "Modyfikuj model"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
msgid "Name"
msgstr "Nazwa"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr "Nowe"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "No attachments linked."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Żaden użytkownik, włącznie z księgowym, nie może edytować zapisów na kontach"
" sprzed tej daty i włącznie z nią. Użyj tego na przykład do zamykania roku "
"podatkowego."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Note"
msgstr "Notatka"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "Nic do zrobienia!"

#. module: account_accountant
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Tylko użytkownicy posiadający rolę \"Księgowych\" mogą edytować zapisy na "
"kontach sprzed tej daty i włącznie z nią. Użyj tego na przykład do "
"zamknięcia okresu i otwarcia roku podatkowego."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "Bilans otwarcia"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Partner"
msgstr "Kontrahent"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Łączenie płatności"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
msgid "Payments"
msgstr "Wpłaty"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Uzgadnianie płatności"

#. module: account_accountant
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill accounts"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification prior to the defined date "
"for all users. As a closed period, all accounting operations are prohibited."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation prior to the defined date. Except for "
"Advisors users."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification prior to the defined date (Journal Entries"
" involving taxes). The Tax Return Lock Date is automatically set when the "
"corresponding Journal Entry is posted."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Process this transaction."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/models/account_move.py:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.server,name:account_accountant.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree_grouped
#, python-format
msgid "Reconcile"
msgstr "Uzgodnij"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_manual_reconciliation
#: model:ir.ui.menu,name:account_accountant.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Uzgodnienie"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr "Uzgadnianie wyciągu bankowego"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "Rejestruj koszt towarów sprzedanych we wpisach do dziennika"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "Odn"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr "Pozostało"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Zapisz"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "Zapisz i nowe"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "Wybierz partnera"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Wybierz partnera lub odpowiednie pozycje "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr "Ustawienia"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "Pomiń"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "Data początkowa"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Taxes"
msgstr "Podatki"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_payment__payment_state_before_switch
msgid ""
"Technical field to keep the value of payment_state when switching from "
"invoicing to accounting (using invoicing_switch_threshold setting field). It"
" allows keeping the former payment state, so that we can restore it if the "
"user misconfigured the switch date and wants to change it."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "To daje średnio"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the accounts on vendor bill lines based on "
"history of previous bills"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "Nie ma nic do uzgodnienia."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Ta strona wyświetla wszystkie transakcje bankowe, które mają być uzgodnione "
"i zapewnia uporządkowwany interfejs aby to zrobić."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Płatność jest zapisana, ale nie jest uzgodniona."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This was the model that generated the lines suggested"
msgstr ""

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr ""

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To Check"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr "Transakcja"

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Transfer Accounts"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#, python-format
msgid "Validate"
msgstr "Zatwierdź"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Write-Off"
msgstr "Odpis"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reconcile the payable and receivable accounts of multiple "
"partners together at the same time."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "You have suspense account moves that match this invoice."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "Uzgodniłeś"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "np. Opłaty bankowe"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "pozostało)"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "sekund na transakcję."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "to mark this invoice as paid."
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "transakcje w"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "niezapłacone faktury"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr ""
