<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_partner_form" model="ir.ui.view">
        <field name="name">res.partner.form</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='is_customer']" position="after">
                <field name="tax_exempted_partner"
                       groups="partner_tax_exemption.access_tax_exempted_partner"/>
                <field name="tax_exemption_reason"
                       groups="partner_tax_exemption.access_tax_exempted_partner"
                       attrs="{'invisible': [('tax_exempted_partner', '=', False)],
                                'required': [('tax_exempted_partner', '=', True)]}"/>
                <field name="tax_exemption_attachment_ids" widget="many2many_attachment_preview"
                       attrs="{'invisible': [('tax_exempted_partner', '=', False)],
                                'required': [('tax_exempted_partner', '=', True)]}"
                       groups="partner_tax_exemption.access_tax_exempted_partner"
                />
            </xpath>
        </field>
    </record>
</odoo>
