# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import _, api, fields, models, Command
from odoo.exceptions import UserError
from datetime import datetime
from odoo.tools import float_round


class SaleOrder(models.Model):
    _inherit = "sale.order"

class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    @api.onchange("order_line", "product_id")
    def product_id_change(self):
        res = super().product_id_change()
        if self.order_id.partner_id and self.order_id.partner_id.tax_exempted_partner:
            self.tax_id = False
            self.tax_id = []
        return res

    def get_printing_sale_tax_amount(self, line):
        if self.order_id.partner_id.tax_exempted_partner:
            for line in self:
                line.tax_id = False
                line.tax_id = []
            return True
        else:
            return super().get_printing_sale_tax_amount(line=line)
