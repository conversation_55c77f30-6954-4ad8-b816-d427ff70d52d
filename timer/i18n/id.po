# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* timer
# 
# Translators:
# <PERSON> <amunif<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:43+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_pause
msgid "Display Timer Pause"
msgstr "Tampilkan Jeda Timer"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_resume
msgid "Display Timer Resume"
msgstr "Tampilkan Lanjutkan Timer"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr "Tampilkan Timer Mulai Utama"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_stop
msgid "Display Timer Stop"
msgstr "Tampilkan Timer Berhenti"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__id
msgid "ID"
msgstr "ID"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__is_timer_running
#: model:ir.model.fields,field_description:timer.field_timer_timer__is_timer_running
msgid "Is Timer Running"
msgstr "Apakah Timer Berjalan"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: timer
#: model:ir.model.constraint,message:timer.constraint_timer_timer_unique_timer
msgid "Only one timer occurrence by model, record and user"
msgstr "Hanya occurence satu kali berdasarkan mdoel, record dan user"

#. module: timer
#. openerp-web
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#, python-format
msgid "Play"
msgstr "Main"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_id
msgid "Res"
msgstr "Res"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_model
msgid "Res Model"
msgstr "Res Model"

#. module: timer
#. openerp-web
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#, python-format
msgid "Start"
msgstr "Mulai"

#. module: timer
#. openerp-web
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#, python-format
msgid "Stop"
msgstr "Stop"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_pause
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_pause
msgid "Timer Last Pause"
msgstr "Timer Jeda Terakhir"

#. module: timer
#: model:ir.model,name:timer.model_timer_mixin
msgid "Timer Mixin"
msgstr "Timer Mixin"

#. module: timer
#: model:ir.model,name:timer.model_timer_timer
msgid "Timer Module"
msgstr "Timer Module"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_start
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_start
msgid "Timer Start"
msgstr "Timer Mulai"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__user_id
msgid "User"
msgstr "Pengguna"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__user_timer_id
msgid "User Timer"
msgstr "Timer User"
