# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* timer
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <ossi.manty<PERSON>@obs-solutions.fi>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:43+0000\n"
"Last-Translator: O<PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_date
msgid "Created on"
msgstr "Luotu"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_pause
msgid "Display Timer Pause"
msgstr "Näytä ajastimen tauko"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_resume
msgid "Display Timer Resume"
msgstr "Näytön ajastimen jatko"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr "Näyttö Ajastimen käynnistys Ensimmäinen kello"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_stop
msgid "Display Timer Stop"
msgstr "Näyttö Ajastin Stop"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__is_timer_running
#: model:ir.model.fields,field_description:timer.field_timer_timer__is_timer_running
msgid "Is Timer Running"
msgstr "Onko ajastin käynnissä"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: timer
#: model:ir.model.constraint,message:timer.constraint_timer_timer_unique_timer
msgid "Only one timer occurrence by model, record and user"
msgstr "Vain yksi ajastimen esiintyminen mallia, tietuetta ja käyttäjää kohti"

#. module: timer
#. openerp-web
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#, python-format
msgid "Play"
msgstr "Toista"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_id
msgid "Res"
msgstr "Res"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_model
msgid "Res Model"
msgstr "Res-malli"

#. module: timer
#. openerp-web
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#, python-format
msgid "Start"
msgstr "Aloita"

#. module: timer
#. openerp-web
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#: code:addons/timer/static/src/js/timer_toggle_button.js:0
#, python-format
msgid "Stop"
msgstr "Lopeta"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_pause
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_pause
msgid "Timer Last Pause"
msgstr "Viimeisin ajastimen tauko"

#. module: timer
#: model:ir.model,name:timer.model_timer_mixin
msgid "Timer Mixin"
msgstr "Ajastin Mixin"

#. module: timer
#: model:ir.model,name:timer.model_timer_timer
msgid "Timer Module"
msgstr "Ajastinmoduuli"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_start
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_start
msgid "Timer Start"
msgstr "Ajastimen käynnistys"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__user_id
msgid "User"
msgstr "Käyttäjä"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__user_timer_id
msgid "User Timer"
msgstr "Käyttäjän ajastin"
