<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Form View to Modify it -->
    <record id="view_inherit_design_manager_wiz" model="ir.ui.view">
        <field name="name">design.assign.manager</field>
        <field name="model">design.assign.manager</field>
        <field name="inherit_id" ref="common_task_process.view_design_manager_approval"/>
        <field name="arch" type="xml">

            <field name="manager_user_id" position="after">
                <field name="is_inter_company_attachment" invisible="1"/>
            </field>
            <field name="attachment_ids_url" position="attributes">
                <attribute name="attrs">{'readonly': [('is_inter_company_attachment', '=', True)]}</attribute>
            </field>

        </field>
    </record>
</odoo>