# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import fields, models, api, _
from odoo.exceptions import UserError
import datetime
import pytz
import textwrap


class SaleOrder(models.Model):
    _inherit = "sale.order"

    def send_site_visit_done_sms(self, user, location):
        """
        Send Site Visit SMS to Customer
        """
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if self.printing_sale and self.partner_id.send_communication_sms and self.partner_id.mobile:
            template = self.env.ref('signjet_sms_process.site_visit_task_sms_done_to_salesperson', False)
            if template:
                template.sms_body = textwrap.dedent("""\
                                    Dear %s\n
                                    The Site Visit task you requested for order reference %s has been completed please check and process with the Sale Order\n
                                    Thanks,\n
                                    %s
                                    """) % (
                    user.name,
                    self.name,
                    self.company_id.name
                )
                message = self.env['sp.sms.template'].render_template(template.sms_body, 'sale.order', self.id)
                message = message.rstrip()
                mobile = self.partner_id.mobile.split(',')
                for mob in mobile:
                    sms_gateway.send_sms(message, mob)
        elif not self.partner_id.mobile:
            raise UserError(_('Please provide a valid mobile number!'))
        else:
            raise UserError(_('Unable to send message!'))
