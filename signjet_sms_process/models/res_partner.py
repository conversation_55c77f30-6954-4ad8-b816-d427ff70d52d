# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import _, api, fields, models, Command
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_round
import textwrap


class Partner(models.Model):
    _inherit = "res.partner"

    send_communication_sms = fields.Boolean(string="Send Communication SMS")
    send_balance_sms = fields.Boolean()

    def action_send_invoice_due_sms(self):
        """
        Send customer SMS for invoices due today
        """
        for partner in self:
            if not partner.mobile:
                continue
            sms_gateway = self.env['sms.gateway'].search([], limit=1)
            if sms_gateway:
                due_invoice_ids = self.env["account.move"].sudo().search([
                    ("partner_id", "=", partner.id),
                    ("amount_residual", ">", 0.0),
                    ("state", "=", "posted"),
                    ("invoice_date_due", "=", fields.Date.today())
                ])
                if not due_invoice_ids:
                    continue
                invoices_name = [inv.name for inv in due_invoice_ids]
                due_invoices = ", ".join(invoices_name)
                template = self.env.ref('signjet_sms_process.account_move_due_follow_up_sms', False)
                if template:
                    template.sms_body = textwrap.dedent("""\
                        Dear %s\n
                        Your invoice(s) has reached its due dates please plan to make the payment of the below invoices\n
                        Check the list of invoices here\n
                        %s\n
                        Thanks,\n
                        %s
                        """) % (
                            partner.name,
                            due_invoices,
                            partner.company_id.name if partner.company_id else ''
                        )
                    message = self.env['sp.sms.template'].render_template(template.sms_body, 'account.move', self.id)
                    message = message.rstrip()
                    sms_gateway.send_sms(message, partner.mobile)

