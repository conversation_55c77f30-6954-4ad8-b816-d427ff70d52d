# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import fields, models, api, _
from odoo.exceptions import UserError
import textwrap


class AccountMove(models.Model):
    _inherit = "account.payment"

    def send_payment_post_sms(self):
        """
        Send customer SMS using dynamic template from sp.sms.template
        """
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if sms_gateway and self.partner_id.mobile:
            template = self.env['sp.sms.template'].search([
                ('model_id', '=', 'account.payment')
            ], limit=1)
            if template:
                currency = self.currency_id.symbol or ''
                total_receivable = self.balance_due - self.amount
                values = {
                    'partner_name': self.partner_id.name or '',
                    'amount': f"{currency}{self.amount}" if self.amount else f"{currency}0.0",
                    'total_receivable': f"{currency}{total_receivable}" if total_receivable else f"{currency}0.0",
                    'company_name': self.company_id.name or '',
                }
                message = template.sms_body.format(**values)
                message = message.rstrip()
                sms_gateway.send_sms(message, self.partner_id.mobile)

    def action_post(self):
        res = super().action_post()
        if self.partner_id.send_communication_sms:
            if not self.sale_id:
                self.send_payment_post_sms()
        return res