# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import api, fields, models
import datetime
import pytz
import textwrap

class Task(models.Model):
    _inherit = "sp.task.process"

    def send_site_visit_done_sms(self):
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if sms_gateway:
            if self.sale_id.user_id.partner_id and self.sale_id.user_id.partner_id.mobile:
                current_time = fields.Datetime.context_timestamp(self, datetime.datetime.now()).strftime('%I:%M %p')
                current_date = fields.Date.today()
                template = self.env.ref('signjet_sms_process.site_visit_task_sms_done', False)
                template.sms_body = """Mudane/Marwo: %s 
                
                Waxaan rajeynayaa in fariintani ay kugu soo gaartay adigoo nabad qaba. 
                Waxaan kuu xaqiijinaynaa in booqashadayada goobta ay dhici doonto %s 
                saacadda %s. Kooxdayadu waxay imaan doonaan goobtaada ee ku taalla 
                %s si ay cabbir kuugu soo qaadaan. %s, %s 
                
                Fadlan noo soo sheeg haddii ay jiraan wax gaar ah oo aad rabto in aan diiradda 
                saarno inta lagu guda jiro booqashada. Haddii isbeddel la rabo, si toos ah noola 
                soo xiriir %s and %s
                Waxaan rajeyneynaa in aan kula kulanno goobta isla markaana hawsha si hufan u socoto.
                Mahadsanid,
                
                %s """ % (
                    self.sale_id.partner_id.name,
                    current_date if current_date else '',
                    current_time if current_time else '',
                    # Time
                    self.location,
                    self.sale_id.user_id.name,
                    self.sale_id.user_id.partner_id.mobile or '',
                    self.sale_id.user_id.name if self.sale_id.user_id else '',
                    self.sale_id.user_id.partner_id.mobile or '',
                    self.env.company.name
                )
                message = self.env['sp.sms.template'].render_template(template.sms_body, 'sp.task.process', self.id)
                message = message.rstrip()
                mobile = self.sale_id.user_id.partner_id.mobile
                sms_gateway.send_sms(message, mobile)

    def action_done_site_task(self):
        """
        inherit for send sms
        """
        res = super().action_done_site_task()
        if self.sale_id.partner_id.send_communication_sms:
            self.sale_id.send_site_visit_done_sms(user=self.user_ids[0], location=self.location)
        return res
