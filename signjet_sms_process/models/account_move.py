# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import fields, models, api, _
import textwrap
from odoo.exceptions import UserError


class AccountMove(models.Model):
    _inherit = "account.move"

    # python
    def send_invoice_due_sms(self):
        """
        Send customer SMS for due invoices using dynamic template
        """
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if sms_gateway and self.partner_id.mobile:
            account_ids = self.env["account.move.line"].sudo().search(
                [
                    ("partner_id", "=", self.partner_id.id),
                    ("company_id", "=", self.company_id.id),
                    ("parent_state", "=", "posted"),
                    ("account_id.internal_type", "=", "receivable"),
                ]
            )
            debit = [acc_id.debit for acc_id in account_ids]
            credit = [acc_id.credit for acc_id in account_ids]
            balance = sum(debit) - sum(credit)
            template = self.env['sp.sms.template'].search([
                ('model_id', '=', 'account.move'),
                ('sms_type', '=', 'due_invoice')
            ], limit=1)
            if not template:
                values = {
                    'partner_name': self.partner_id.name or '',
                    'due_invoices': self.name or '',
                    'balance': balance or '',
                    'company_name': self.company_id.name or '',
                }
                message = template.sms_body.format(**values)
                message = message.rstrip()
                mobile = self.partner_id.mobile
                sms_gateway.send_sms(message, mobile)

    def send_invoice_posted_sms(self):
        """
        Send customer SMS for credit invoice using dynamic template
        """
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if sms_gateway and self.partner_id.mobile:
            account_ids = self.env["account.move.line"].sudo().search(
                [
                    ("partner_id", "=", self.partner_id.id),
                    ("company_id", "=", self.company_id.id),
                    ("parent_state", "=", "posted"),
                    ("account_id.internal_type", "=", "receivable"),
                ]
            )
            debit = [acc_id.debit for acc_id in account_ids]
            credit = [acc_id.credit for acc_id in account_ids]
            balance = sum(debit) - sum(credit)

            template = self.env['sp.sms.template'].search([
                ('model_id', '=', 'account.move'),
                ('sms_type', '=', 'invoice')
            ], limit=1)
            if template:
                currency = self.currency_id.symbol or ''
                values = {
                    'partner_name': self.partner_id.name or '',
                    'amount_residual': f"{currency}{self.amount_residual}" if self.amount_residual else '',
                    'name': self.name or '',
                    'balance': f"{currency}{balance}" if balance else '',
                    'company_name': self.company_id.name or '',
                }
                message = template.sms_body.format(**values)
                message = message.rstrip()
                mobile = self.partner_id.mobile
                sms_gateway.send_sms(message, mobile)

    def action_post(self):
        res = super().action_post()
        if self.move_type in ("out_invoice", "in_invoice") and self.partner_id.send_communication_sms:
            if self.sale_id.adv_payment_amt < self.amount_total:
                self.send_invoice_posted_sms()
        return res

