# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields, models, api, _
import textwrap

class SpAssignTask(models.TransientModel):
    _inherit = "ps.receive.delivery.wiz"

    def send_delivery_receive_sms(self, active_id, partner):
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if sms_gateway:
            if partner.mobile:
                template = self.env.ref('signjet_sms_process.printing_sale_receive_delivery_sms', False)
                if template:
                    template.sms_body = textwrap.dedent("""\
                                            Dear %s,\n
                                            We’re pleased to inform you that your items are now ready for delivery.\n
                                            Order Details:\n
                                            SO Number: %s,\n
                                            Items: %s,\n
                                            Thank you for choosing us. We look forward to serving you.\n
                                            Mudane/Marwo %s,\n
                                            Waxaan ku faraxsanahay inaan kugu wargelino in alaabtaada ay diyaar tahay.\n
                                            Faahfaahinta Dalabka:\n
                                            Lambarka Dalabka: %s\n
                                            Waad ku mahadsan tahay doorashada aad na dooratay. Waxaan rajeynaynaa inaan kuugu sii adeegi doono mustaqbalka sida ugu wanaagsan.
                                            """) % (
                        active_id.partner_id.name,
                        active_id.name,
                        ', '.join(
                            line.task_id.order_product_id.name
                            for line in self.delivery_receive_line
                            if line.task_id and line.task_id.order_product_id
                        ),
                        active_id.partner_id.name,
                        active_id.name
                    )

                    message = self.env['sp.sms.template'].render_template(
                        template.sms_body, 'ps.receive.delivery.wiz', self.id
                    ).strip()
                    mobile = partner.mobile
                    sms_gateway.send_sms(message, mobile)

    def action_confirm(self):
        """
        inherit for send sms to assign users
        """
        res = super().action_confirm()
        active_id = self.env['sale.order'].sudo().browse(self._context.get('active_ids'))
        if active_id and active_id.partner_id.send_communication_sms:
            self.send_delivery_receive_sms(active_id=active_id, partner=active_id.partner_id)
        return res


