# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import fields, models, api, _
import datetime
import pytz
import textwrap
from odoo.exceptions import UserError


class SpAssignTask(models.TransientModel):
    _inherit = "assign.site.visit.task"

    def send_site_visit_assign_sms(self, user, active_id):
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if sms_gateway:
            current_time = fields.Datetime.context_timestamp(self, datetime.datetime.now()).strftime('%I:%M %p')
            current_date = fields.Date.today()
            assignee = self.site_visit_user_line.filtered(lambda ln: ln.is_site_visit_user)[:1]
            if assignee and assignee.user_id.partner_id and assignee.user_id.partner_id.mobile:
                # Fetch template from sp.sms.template
                template = self.env['sp.sms.template'].search([
                    ('model_id', '=', 'assign.site.visit.task'),
                    ('sms_type', '=', 'site_assign_to_user')  # Optional: filter by sms_type if needed
                ], limit=1)
                if template:
                    values = {
                        'customer_name': active_id.partner_id.name or '',
                        'contact': active_id.contact_person_id.mobile if active_id.contact_person_id else active_id.partner_id.name,
                        'location': self.location or '',
                        'date': current_date or '',
                        'time': current_time or '',
                    }
                    message = template.sms_body.format(**values)
                    message = message.rstrip()
                    mobile = assignee.user_id.partner_id.mobile
                    sms_gateway.send_sms(message, mobile)

    # python
    def send_site_visit_customer_sms(self, active_id, user):
        """
        Send customer SMS for site visit using dynamic template
        """
        sms_gateway = self.env['sms.gateway'].search([], limit=1)
        if sms_gateway:
            mobile = self.tele_no or active_id.partner_id.mobile
            if mobile:
                current_time = fields.Datetime.context_timestamp(self, datetime.datetime.now()).strftime('%I:%M %p')
                current_date = fields.Date.today()
                # Fetch template from sp.sms.template
                template = self.env['sp.sms.template'].search([
                    ('model_id', '=', 'assign.site.visit.task'),
                    ('sms_type', '=', 'site_assign_customer')
                ], limit=1)
                if template:
                    values = {
                        'customer_name': active_id.partner_id.name or '',
                        'date': self.date or '',
                        'time': self.time or '',
                        'location': self.location or '',
                        'user_name': user.name or '',
                        'user_mobile': user.partner_id.mobile or '',
                        'assigned_user': active_id.user_id.name if active_id.user_id else 'N/A',
                        'contact_mobile': self.tele_no or active_id.partner_id.mobile or '',
                        'company_name': self.env.company.name or '',
                    }
                    message = template.sms_body.format(**values)
                    message = message.rstrip()
                    sms_gateway.send_sms(message, mobile)

    # def send_site_visit_assign_sms(self, user, active_id):
    #     sms_gateway = self.env['sms.gateway'].search([], limit=1)
    #     if sms_gateway:
    #         current_time = fields.Datetime.context_timestamp(self, datetime.datetime.now()).strftime('%I:%M %p')
    #         current_date = fields.Date.today()
    #         assignee = self.site_visit_user_line.filtered(lambda ln: ln.is_site_visit_user)[:1]
    #         if assignee and assignee.user_id.partner_id and assignee.user_id.partner_id.mobile:
    #             template = self.env.ref('signjet_sms_process.site_visit_assign_sms_to_assignee', False)
    #             if template:
    #                 template.sms_body = textwrap.dedent("""\
    #                                 Fadlan Cabirka u soo qaad\n
    #                                 %s, %s\n
    #                                 %s\n
    #                                 %s\n
    #                                 %s
    #                                 """) % (
    #                     active_id.partner_id.name,  # Customer Name
    #                     active_id.contact_person_id.mobile if active_id.contact_person_id else active_id.partner_id.name,
    #                     # Contact
    #                     self.location if self.location else '',
    #                     # Location
    #                     current_date if current_date else '',
    #                     current_time if current_time else ''
    #                 # Time
    #                 )
    #                 message = self.env['sp.sms.template'].render_template(template.sms_body, 'assign.site.visit.task',
    #                                                                       self.id)
    #                 message = message.rstrip()
    #                 mobile = assignee.user_id.partner_id.mobile
    #                 sms_gateway.send_sms(message, mobile)

    # def send_site_visit_customer_sms(self, active_id, user):
    #     """
    #     Send customer SMS for site visit
    #     """
    #     sms_gateway = self.env['sms.gateway'].search([], limit=1)
    #     if sms_gateway:
    #         if active_id.partner_id.mobile or self.tele_no:
    #             current_time = fields.Datetime.context_timestamp(self, datetime.datetime.now()).strftime('%I:%M %p')
    #             current_date = fields.Date.today()
    #             template = self.env.ref('signjet_sms_process.site_visit_task_send_customer', False)
    #             if template:
    #                 template.sms_body = textwrap.dedent("""\
    #                                         Mudane/Marwo: %s\n
    #                                         Waxaan rajeynayaa in fariintani ay kugu soo gaartay adigoo nabad qaba.\n
    #                                         Waxaan kuu xaqiijinaynaa in booqashadayada goobta ay dhici doonto %s saacadda %s.\n
    #                                         Kooxdayadu waxay imaan doonaan goobtaada ee ku taalla %s si ay cabbir kuugu soo qaadaan. %s, %s\n\n
    #                                         Fadlan noo soo sheeg haddii ay jiraan wax gaar ah oo aad rabto in aan diiradda saarno inta lagu guda jiro booqashada.\n
    #                                         Haddii isbeddel la rabo, si toos ah noola soo xiriir %s, %s\n\n
    #                                         Waxaan rajeyneynaa in aan kula kulanno goobta isla markaana hawsha si hufan u socoto.\n\n
    #                                         Mahadsanid,\n\n
    #                                         %s
    #                                         """) % (
    #                     active_id.partner_id.name,  # Customer Name
    #                     self.date if self.date else '',  # Date
    #                     self.time if self.time else '',
    #                     # Time
    #                     self.location if self.location else False,
    #                     user.name,
    #                     user.partner_id.mobile,
    #                     active_id.user_id.name if active_id.user_id else 'N/A',
    #                     self.tele_no if self.tele_no else active_id.partner_id.mobile,
    #                     self.env.company.name
    #                 )
    #                 message = self.env['sp.sms.template'].render_template(template.sms_body, 'assign.site.visit.task',
    #                                                                       self.id)
    #                 message = message.rstrip()
    #                 mobile = self.tele_no
    #                 sms_gateway.send_sms(message, mobile)

    def action_confirm(self):
        """
        inherit for send sms to assign users
        """
        res = super().action_confirm()
        active_id = self.env['sale.order'].sudo().browse(self._context.get('active_ids'))
        if active_id and self.site_visit_order_ids:
            user = [user.user_id for user in self.site_visit_user_line if user.is_site_visit_user]
            if user.partner_id.send_communication_sms:
                self.send_site_visit_assign_sms(user=user[0], active_id=active_id)
            if active_id.partner_id.send_communication_sms:
                self.send_site_visit_customer_sms(active_id=active_id, user=user[0])
        return res

