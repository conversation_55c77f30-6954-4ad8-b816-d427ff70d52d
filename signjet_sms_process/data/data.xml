<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="site_visit_task_send_customer" model="sp.sms.template">
            <field name="name">Site visit task send customer</field>
            <field name="model_id" ref="common_task_process.model_assign_site_visit_task"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="site_visit_assign_sms_to_assignee" model="sp.sms.template">
            <field name="name">Site visit assign sms</field>
            <field name="model_id" ref="common_task_process.model_assign_site_visit_task"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="site_visit_task_sms_done" model="sp.sms.template">
            <field name="name">Site visit task done sms</field>
            <field name="model_id" ref="common_task_process.model_sp_task_process"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="site_visit_task_sms_done_to_salesperson" model="sp.sms.template">
            <field name="name">Site visit task done sms to salesperson</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="account_move_posted_sms" model="sp.sms.template">
            <field name="name">Invoice Posted SMS</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="account_move_due_follow_up_sms" model="sp.sms.template">
            <field name="name">Invoice Due Date Follow-up</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="account_move_credit_invoice_posted_sms" model="sp.sms.template">
            <field name="name">Credit Invoice Posted SMS</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="account_move_advance_payment_posted_sms" model="sp.sms.template">
            <field name="name">Advance Payment Posted SMS</field>
            <field name="model_id" ref="sale_liability_advance_pay.model_sale_liability_adv_pay_wz"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="account_payment_posted_sms" model="sp.sms.template">
            <field name="name">Payment Posted SMS</field>
            <field name="model_id" ref="account.model_account_payment"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="printing_sale_receive_delivery_sms" model="sp.sms.template">
            <field name="name">Receive Delivery</field>
            <field name="model_id" ref="sale_process.model_ps_receive_delivery_wiz"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

        <record id="partner_bulk_sms" model="sp.sms.template">
            <field name="name">Send Bulk SMS</field>
            <field name="model_id" ref="signjet_sms_process.model_bulk_sms"/>
            <field name="sms_body"><![CDATA[
            ]]>
            </field>
        </record>

    </data>
</odoo>
