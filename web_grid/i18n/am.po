# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_grid
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Amharic (https://www.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/js/grid_controller.js:155
#: code:addons/web_grid/static/src/xml/grid_view.xml:5
#, python-format
msgid "Add a Line"
msgstr ""

#. module: web_grid
#: code:addons/web_grid/models.py:218 code:addons/web_grid/models.py:250
#, python-format
msgid "Can not use fields of type %s as grid columns"
msgstr ""

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/js/grid_view.js:14
#, python-format
msgid "Grid"
msgstr ""

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/js/grid_renderer.js:199
#, python-format
msgid "See all the records aggregated in this cell"
msgstr ""

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/js/grid_renderer.js:137
#, python-format
msgid ""
"The sectioned grid view can't handle groups with different columns sets"
msgstr ""

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/xml/grid_view.xml:19
#, python-format
msgid "Today"
msgstr ""

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/js/grid_renderer.js:337
#: code:addons/web_grid/static/src/js/grid_renderer.js:342
#, python-format
msgid "Total"
msgstr ""

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/js/grid_controller.js:210
#, python-format
msgid "Undefined"
msgstr ""

#. module: web_grid
#. openerp-web
#: code:addons/web_grid/static/src/js/grid_renderer.js:258
#, python-format
msgid "Unknown"
msgstr ""

#. module: web_grid
#: model:ir.model,name:web_grid.model_base
msgid "base"
msgstr ""

#. module: web_grid
#: model:ir.model,name:web_grid.model_ir_actions_act_window_view
msgid "ir.actions.act_window.view"
msgstr ""

#. module: web_grid
#: model:ir.model,name:web_grid.model_ir_ui_view
msgid "ir.ui.view"
msgstr ""
