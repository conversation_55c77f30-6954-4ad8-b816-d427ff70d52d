# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import UserError

class AccountPayment(models.Model):
    _inherit = 'account.payment'
    
    overpayment_reason = fields.Char(
        string='Overpayment Reason',tracking=True,
    )

    def action_post(self):
        if self.state == "draft" and self.amount > self.balance_due and not self.overpayment_reason:
            return {
                    'type': 'ir.actions.act_window',
                    'res_model': 'account.payment.extra.reason.wizard',
                    'view_mode': 'form',
                    'target': 'new',
                    'context': {
                    'default_payment_id': self.id,
                    'default_payment_amount': self.amount,
                    'default_due_amount': self.balance_due,
                    },
            }
        return super(AccountPayment, self).action_post()

    def action_open_payment_post_wizard(self):
        res = super().action_open_payment_post_wizard()
        res["context"].update(
            {
                'default_due_amount': self.balance_due,
            }
        )
        if self.amount > self.balance_due:
            res["context"].update(
                {
                    'default_is_reason': True,
                }
            )
        return res

    def action_vendor_payment_send_for_approval(self):
        res = super().action_vendor_payment_send_for_approval()
        res["context"].update(
            {
                'default_due_amount': self.balance_due,
                'default_payment_amount': self.amount,
            }
        )
        if self.amount > self.balance_due:
            res["context"].update(
                {
                    'default_is_reason': True,
                }
            )
        return res
