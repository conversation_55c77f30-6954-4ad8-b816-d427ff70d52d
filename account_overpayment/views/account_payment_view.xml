<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="inherit_reason_in_view_account_payment_form" model="ir.ui.view">
        <field name="name">inherit.reason.in.account.payment.form</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <field name="ref" position="after">
                <field name="overpayment_reason"
                       attrs="{'invisible': ['|', ('state', '!=', 'posted'), ('overpayment_reason', '=', False)], 'readonly': True}"/>

            </field>
        </field>
    </record>
</odoo>
