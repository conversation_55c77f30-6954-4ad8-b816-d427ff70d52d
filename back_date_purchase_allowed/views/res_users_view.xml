<?xml version="1.0" encoding="UTF-8" ?>
<data>
    <odoo>
        <record id="res_user_back_date_purchase_inherit" model="ir.ui.view">
            <field name="name">res.user.view.inherit.back.date.purchase</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <group name="messaging" position="before">
                    <group string="Purchase Back Date Restriction" groups="back_date_purchase_allowed.group_allow_purchase_back_date">
                        <field name="allowed_back_date_purchase" placeholder="Check here to make days restriction for back date purchase"/>
                        <field name="back_date_purchase_days" attrs="{'invisible': [('allowed_back_date_purchase', '=', False)]}"/>
                    </group>
                </group>
            </field>
        </record>
    </odoo>
</data>