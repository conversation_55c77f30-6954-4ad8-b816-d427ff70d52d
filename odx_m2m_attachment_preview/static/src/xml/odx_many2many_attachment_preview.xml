<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="FieldAttachmentFileUploader.attachment_preview">
        <t t-set="url" t-value="widget.metadata[file.id] ? widget.metadata[file.id].url : false" />
        <t t-if="file.data" t-set="file" t-value="file.data" />
        <t t-set="editable" t-value="widget.mode === 'edit'" />
        <t t-if="file.mimetype" t-set="mimetype" t-value="file.mimetype" />
        <div t-attf-class="o_attachment o_attachment_many2many #{ editable ? 'o_attachment_editable' : '' } #{upload ? 'o_attachment_uploading' : ''}" t-att-title="file.name">
            <div class="o_attachment_wrap" t-att-data-id="file.id" style="cursor: pointer;">
                <t t-set="ext" t-value="file.name.replace(/^.*\./, '')" />
                <div class="o_image_box float-left" t-att-data-id="file.id">
                    <a t-att-href="url" t-att-title="'Download ' + file.name" aria-label="Download">
                        <span class="o_image o_hover" t-att-data-mimetype="mimetype" t-att-data-ext="ext" role="img" t-attf-data-src="/web/content/{{file.id}}" />
                    </a>
                </div>

                <div class="caption">
                    <a class="ml4" t-att-href="url" t-att-title="'Download ' + file.name">
                        <t t-esc='file.name' />
                    </a>
                </div>
                <div class="caption small">
                    <a class="ml4 small text-uppercase" t-att-href="url" t-att-title="'Download ' + file.name">
                        <b>
                            <t t-esc='ext' />
                        </b>
                    </a>
                    <div t-if="editable" class="progress o_attachment_progress_bar">
                        <div class="progress-bar progress-bar-striped active" style="width: 100%">Uploading</div>
                    </div>
                </div>

                <div t-if="editable" class="o_attachment_uploaded">
                    <i class="text-success fa fa-check" role="img" aria-label="Uploaded" title="Uploaded" />
                </div>
                <div t-if="editable" class="o_attachment_delete" t-att-data-id="file.id">
                    <span class="text-white" role="img" aria-label="Delete" title="Delete">×</span>
                </div>
            </div>
        </div>
    </t>

    <div t-name="FieldAttachmentFileUploader.files" class="o_attachments" aria-atomic="true">
        <!-- uploaded files -->
        <t t-foreach="widget.value.data" t-as="file">
            <t t-if="!file.data.upload" t-call="FieldAttachmentFileUploader.attachment_preview" />
        </t>
        <!-- uploading files -->
        <t t-foreach="widget.uploadingFiles" t-as="file">
            <t t-set="upload" t-value="true" />
            <t t-call="FieldAttachmentFileUploader.attachment_preview" />
        </t>
    </div>

</templates>