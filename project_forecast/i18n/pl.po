# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_forecast
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <dare<PERSON>@krokus.com.pl>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <sz<PERSON>zak<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <luka<PERSON>.g<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:01+0000\n"
"PO-Revision-Date: 2021-09-14 12:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_form_inherit_project_forecast
msgid "<span class=\"o_stat_text\">Forecast</span>"
msgstr ""

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_task_view_form
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_form_inherit_project_forecast
msgid "<span class=\"o_stat_value\">Hours</span>"
msgstr ""

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.period_report_template
msgid "ASSIGN ME THIS SHIFT"
msgstr "PRZYPISZ DO MNIE TĄ ZMIANĘ"

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_project_task__allow_forecast
msgid "Allow Planning"
msgstr ""

#. module: project_forecast
#: model:ir.ui.menu,name:project_forecast.planning_menu_schedule_by_project
msgid "By Project"
msgstr "Wg projektu"

#. module: project_forecast
#: model:ir.model.fields,help:project_forecast.field_planning_slot__allow_forecast
#: model:ir.model.fields,help:project_forecast.field_project_project__allow_forecast
#: model:ir.model.fields,help:project_forecast.field_project_task__allow_forecast
msgid "Enable planning tasks on the project."
msgstr ""

#. module: project_forecast
#: code:addons/project_forecast/models/project.py:0
#: model_terms:ir.ui.view,arch_db:project_forecast.project_task_view_form
#, python-format
msgid "Forecast"
msgstr "Prognoza"

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__forecast_hours
#: model:ir.model.fields,field_description:project_forecast.field_project_task__forecast_hours
msgid "Forecast Hours"
msgstr ""

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_task_view_form
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_form_inherit_project_forecast
msgid "Forecasts"
msgstr "Prognozy"

#. module: project_forecast
#: model:ir.model.constraint,message:project_forecast.constraint_planning_slot_project_required_if_task
msgid "If the planning is linked to a task, the project must be set too."
msgstr ""

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__planned_hours
msgid "Initially Planned Hours"
msgstr "Wstępnie zaplanowane godziny"

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.action_project_task_view_planning
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_from_project
msgid "Let's start your planning by adding a new shift."
msgstr "Zacznijmy planowanie od dodania nowej zmiany."

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.action_project_task_view_planning
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_by_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_from_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_schedule_by_employee
msgid "No shifts found. Let's create one!"
msgstr "Nie znaleziono zmian. Utwórz nową!"

#. module: project_forecast
#: model:ir.model.fields,help:project_forecast.field_planning_slot__forecast_hours
msgid "Number of hours already forecast for this task (and its sub-tasks)."
msgstr ""

#. module: project_forecast
#: model:ir.model.fields,help:project_forecast.field_project_task__forecast_hours
msgid ""
"Number of hours forecast for this task (and its sub-tasks), rounded to the "
"unit."
msgstr ""

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__parent_id
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_view_search
msgid "Parent Task"
msgstr "Zadanie nadrzędne"

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_form_inherit_project_forecast
msgid "Plan your resources on project tasks"
msgstr "Zaplanuj zasoby na zadaniach projektu"

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_form_inherit_project_forecast
msgid "Planned Date"
msgstr "Planowana data"

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.project_forecast_action_from_project
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__allow_forecast
#: model:ir.model.fields,field_description:project_forecast.field_project_project__allow_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_kanban_inherit_project_forecast
msgid "Planning"
msgstr "Planowanie"

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_slot
msgid "Planning Shift"
msgstr "Planowanie zmian"

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_project_project
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__project_id
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot_template__project_id
#: model_terms:ir.ui.view,arch_db:project_forecast.period_report_template
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_view_search
msgid "Project"
msgstr "Projekt"

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__resource_id
msgid "Resource"
msgstr "Zasób"

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.planning_action_schedule_by_project
msgid "Schedule by Project"
msgstr "Harmonogram według projektu"

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.project_forecast_action_schedule_by_employee
msgid "Schedule by Resource"
msgstr "Harmonogram według zasobów"

#. module: project_forecast
#: code:addons/project_forecast/controllers/main.py:0
#, python-format
msgid "Shift"
msgstr "Zmiana"

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_slot_template
msgid "Shift Template"
msgstr "Szablon zmian"

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_by_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_schedule_by_employee
msgid ""
"Shifts are periods allocated to your resources for a specific role. "
"Together, they constitute your resources' planning."
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_project_task
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__task_id
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot_template__task_id
#: model_terms:ir.ui.view,arch_db:project_forecast.period_report_template
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_view_search
msgid "Task"
msgstr "Zadanie"

#. module: project_forecast
#: model:ir.model.fields,help:project_forecast.field_planning_slot__planned_hours
msgid "Time planned to achieve this task (including its sub-tasks)."
msgstr ""

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_project_project__total_forecast_time
msgid "Total Forecast Time"
msgstr "Całkowity prognozowany czas"

#. module: project_forecast
#: model:ir.model.fields,help:project_forecast.field_project_project__total_forecast_time
msgid "Total number of forecast hours in the project rounded to the unit."
msgstr ""
"Całkowita liczba prognozowanych godzin w projekcie, zaokrąglona do "
"jednostki."

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.action_project_task_view_planning
msgid "View Planning"
msgstr "Zobacz planowanie"

#. module: project_forecast
#: code:addons/project_forecast/models/project.py:0
#, python-format
msgid ""
"You cannot delete a project containing plannings. You can either delete all "
"the project's forecasts and then delete the project or simply deactivate the"
" project."
msgstr ""

#. module: project_forecast
#: code:addons/project_forecast/models/project.py:0
#, python-format
msgid ""
"You cannot delete a task containing plannings. You can either delete all the"
" task's plannings and then delete the task or simply deactivate the task."
msgstr ""

#. module: project_forecast
#: code:addons/project_forecast/models/project_forecast.py:0
#, python-format
msgid "Your task is not in the selected project."
msgstr "Twoje zadanie nie znajduje się w wybranym projekcie."
