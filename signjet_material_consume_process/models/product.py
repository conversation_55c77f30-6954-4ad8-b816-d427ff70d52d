# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields, models, api, _


class Product(models.Model):
    _inherit = 'product.template'

    production_product_lines = fields.One2many('production.product.line', 'product_tmpl_id')
    workshop_product_lines = fields.One2many('workshop.product.line', 'product_tmpl_id')
    sticker_product_lines = fields.One2many('sticker.product.line', 'product_tmpl_id')


class ProductionLine(models.Model):
    _name = 'production.product.line'
    _description = 'Production Line'

    product_tmpl_id = fields.Many2one('product.template')
    product_id = fields.Many2one('product.product', 'Product')
    uom_id = fields.Many2one('uom.uom', 'UOM')
    qty = fields.Float('Quantity', default=1.0)

    @api.onchange('product_id')
    def onchange_product(self):
        self.uom_id = self.product_id.uom_id.id


class WorkshopLine(models.Model):
    _name = 'workshop.product.line'
    _description = 'Workshop Line'

    product_tmpl_id = fields.Many2one('product.template')
    product_id = fields.Many2one('product.product', 'Product')
    uom_id = fields.Many2one('uom.uom', 'UOM')
    qty = fields.Float('Quantity', default=1.0)

    @api.onchange('product_id')
    def onchange_product(self):
        self.uom_id = self.product_id.uom_id.id


class StickerLine(models.Model):
    _name = 'sticker.product.line'
    _description = 'Sticker Line'

    product_tmpl_id = fields.Many2one('product.template')
    product_id = fields.Many2one('product.product', 'Product')
    uom_id = fields.Many2one('uom.uom', 'UOM')
    qty = fields.Float('Quantity', default=1.0)

    @api.onchange('product_id')
    def onchange_product(self):
        self.uom_id = self.product_id.uom_id.id
