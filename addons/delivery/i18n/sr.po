# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: delivery
#: model:mail.template,report_name:delivery.mail_template_data_delivery_confirmation
msgid "${(object.name or '').replace('/','_')}"
msgstr ""

#. module: delivery
#: model:mail.template,subject:delivery.mail_template_data_delivery_confirmation
msgid ""
"${object.company_id.name} Delivery Order (Ref ${object.name or 'n/a' })"
msgstr ""

#. module: delivery
#: model:mail.template,body_html:delivery.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello ${object.partner_id.name},<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        %if object.carrier_tracking_ref:\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            %if object.carrier_tracking_url:\n"
"                <a href=\"${object.carrier_tracking_url}\" target=\"_blank\">${object.carrier_tracking_ref}</a>.\n"
"            %else:\n"
"                ${object.carrier_tracking_ref}.\n"
"            %endif\n"
"            </strong>\n"
"        %endif\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,<br/>\n"
"        % if user and user.signature:\n"
"          ${user.signature | safe}\n"
"        % endif\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "<i class=\"fa fa-arrow-right\"/> Add to order"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "<i class=\"fa fa-arrow-right\"/> Get rate"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:77
#, python-format
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "<span>kg</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Carrier</strong>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>Shipping Weight</strong>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>Tracking Number</strong>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Weight</strong>"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "Активно"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "Iznos"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__available_carrier_ids
msgid "Available Carriers"
msgstr ""

#. module: delivery
#: selection:delivery.carrier,delivery_type:0
msgid "Based on Rules"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Otkaži"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model:ir.model.fields,field_description:delivery.field_product_packaging__package_carrier_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "Prevoznik"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "Kompanija"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "Uslov"

#. module: delivery
#: model:ir.model,name:delivery.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "Zemlje"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
msgid "Created by"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr ""

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr ""

#. module: delivery
#: model:product.category,name:delivery.product_category_deliveries
msgid "Deliveries"
msgstr ""

#. module: delivery
#: model:ir.ui.menu,name:delivery.menu_delivery
msgid "Delivery"
msgstr "Isporuka"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Delivery Information"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
msgid "Delivery Method"
msgstr "Načini isporuke"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.model,name:delivery.model_delivery_carrier
#: model:ir.ui.menu,name:delivery.menu_action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Delivery Methods"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr ""

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_packaging_view
#: model:ir.ui.menu,name:delivery.menu_delivery_packagings
#: model_terms:ir.ui.view,arch_db:delivery.product_packaging_delivery_tree
msgid "Delivery Packages"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__delivery_packaging_id
#: model_terms:ir.ui.view,arch_db:delivery.product_packaging_delivery_form
msgid "Delivery Packaging"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "Proizvod za Isporuku"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_rating_success
msgid "Delivery Rating Success"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination Availability"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
msgid "Display Name"
msgstr ""

#. module: delivery
#: model:ir.actions.act_window,name:delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr ""

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"                UPS Express, UPS Standard) with a set of pricing rules attached\n"
"                to each method."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:217
#: code:addons/delivery/models/delivery_grid.py:50
#, python-format
msgid "Error: this delivery method is not available for this address."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_price
msgid "Estimated Delivery Price"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to filter delivery carriers according to the "
"delivery address of your customer."
msgstr ""

#. module: delivery
#: selection:delivery.carrier,delivery_type:0
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
msgid "Fixed Price"
msgstr ""

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Free delivery charges"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr ""

#. module: delivery
#: selection:delivery.carrier,integration_level:0
msgid "Get Rate"
msgstr ""

#. module: delivery
#: selection:delivery.carrier,integration_level:0
msgid "Get Rate and Create Shipment"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Grupiši po"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging__height
msgid "Height"
msgstr "Visina"

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Height must be positive"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:126
#, python-format
msgid ""
"Info:\n"
"The shipping is free because the order amount exceeds %.2f.\n"
"(The actual shipping cost is: %.2f)"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__invoice_shipping_on_delivery
msgid "Invoice Shipping on Delivery"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule____last_update
msgid "Last Modified on"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
msgid "Last Updated by"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
msgid "Last Updated on"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging__length
msgid "Length"
msgstr "Dužina"

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Length must be positive"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "Marza"

#. module: delivery
#: sql_constraint:delivery.carrier:0
msgid "Margin cannot be lower than -100%"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging__max_weight
msgid "Max Weight"
msgstr ""

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Max Weight must be positive"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "Maksimalna Vrednost"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_packaging__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Name"
msgstr "Ime"

#. module: delivery
#: selection:product.packaging,package_carrier_type:0
msgid "No carrier integration"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/sale_order.py:80
#, python-format
msgid "No carrier set for this order."
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:102
#, python-format
msgid "No price rule matching this order; delivery cost cannot be computed."
msgstr ""

#. module: delivery
#: model:delivery.carrier,name:delivery.normal_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_normal
#: model:product.template,name:delivery.product_product_delivery_normal_product_template
msgid "Normal Delivery Charges"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "U redu"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "Operator"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Package"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging__shipper_package_code
msgid "Package Code"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:35
#: code:addons/delivery/models/stock_picking.py:122
#, python-format
msgid "Package Details"
msgstr ""

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:52
#, python-format
msgid "Package too heavy!"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_stock_quant_package
#: model:ir.model.fields,field_description:delivery.field_stock_picking__package_ids
msgid "Packages"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__stock_quant_package_id
msgid "Physical Package"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/sale_order.py:82
#, python-format
msgid ""
"Please use \"Check price\" in order to compute a shipping price for this "
"quotation."
msgstr ""

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
msgid "Price"
msgstr "Cena"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_product_packaging
msgid "Product Packaging"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr ""

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Quantity"
msgstr "Kolicina"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sale Order"
msgstr "Nalog za prodaju"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
msgid "Sale Price"
msgstr "Prodajne cene"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Save"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send Confirmation Email"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "Niz"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:171
#, python-format
msgid ""
"Shipment sent to carrier %s for shipping with tracking number %s<br/>Cost: "
"%.2f %s"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__shipping_weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "Shipping Weight:"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "Stanja"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move
msgid "Stock Move"
msgstr "Premesti skladiste"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_poste
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr ""

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:53
#, python-format
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:111
#, python-format
msgid "There is no matching delivery rule."
msgstr ""

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"                according to your settings; on the sales order (based on the\n"
"                quotation) or the invoice (based on the delivery orders)."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
msgid "This delivery method will be used when invoicing from picking."
msgstr "Metod dostave ce se koristiti kada fakturises iz izborne liste."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Praćenje"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_uom_id
msgid "Unit of Measure"
msgstr "Jedinica Mere"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight_uom_id
msgid "Unit of measurement for Weight"
msgstr ""

#. module: delivery
#: model:product.product,uom_name:delivery.product_product_delivery
#: model:product.product,uom_name:delivery.product_product_delivery_normal
#: model:product.product,uom_name:delivery.product_product_delivery_poste
#: model:product.template,uom_name:delivery.product_product_delivery_normal_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_poste_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_product_template
msgid "Unit(s)"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "Promenljiva"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "Varijabilni Faktor"

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
#: model:ir.model.fields,field_description:delivery.field_stock_picking__volume
msgid "Volume"
msgstr "Obim"

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
#: model:ir.model.fields,field_description:delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Težina"

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
msgid "Weight * Volume"
msgstr "Težina * Zapremina"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__weight
msgid "Weight computed based on the sum of the weights of the products."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__shipping_weight
msgid "Weight used to compute the price of the delivery (if applicable)."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "Weight:"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging__width
msgid "Width"
msgstr "Širina"

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Width must be positive"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:222
#, python-format
msgid ""
"You are shipping different packaging types in the same shipment.\n"
"Packaging Types: %s"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/sale_order.py:78
#, python-format
msgid "You can add delivery price only on unconfirmed quotations."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:185
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_from
msgid "Zip From"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_to
msgid "Zip To"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr ""

#. module: delivery
#: model:product.product,weight_uom_name:delivery.product_product_delivery
#: model:product.product,weight_uom_name:delivery.product_product_delivery_normal
#: model:product.product,weight_uom_name:delivery.product_product_delivery_poste
#: model:product.template,weight_uom_name:delivery.product_product_delivery_normal_product_template
#: model:product.template,weight_uom_name:delivery.product_product_delivery_poste_product_template
#: model:product.template,weight_uom_name:delivery.product_product_delivery_product_template
msgid "kg"
msgstr "kg"
