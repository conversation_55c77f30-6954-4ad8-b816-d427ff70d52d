<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="event_product_template_public" model="ir.rule">
        <field name="name">Product template linked to event: Public</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="domain_force">[('product_variant_ids.event_ticket_ids.event_id.website_published', '=', True)]</field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo>
