# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-10 14:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022\n"
"Language-Team: <PERSON> Bokmål (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr "<b>Beløp:</b>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr "<b>Referanse:</b>"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>Betal til: </h3><ul><li>Bank: %s</li><li>Kontonummer: "
"%s</li><li>Konoinnehaver: %s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Tilbake til min konto"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr "<i class=\"fa fa-trash\"/> Slett"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"
msgstr ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span><i class=\"fa fa-arrow-right\"/> Get my Stripe keys</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span><i class=\"fa fa-arrow-right\"/> How to configure your PayPal "
"account</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span>Start selling directly without an account; an email will be sent by "
"Paypal to create your new account and collect your payments.</span>"
msgstr ""
"<span>Begynn å selge direkte, uten en konto: du vil motta en e-post fra "
"PayPal om å opprette en ny konto og å motta penger.</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid "<strong>No suitable payment acquirer could be found.</strong>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Ingen egnet betalingsmetode ble funnet.</strong><br/>\n"
"                                Hvis du tror dette skyldes en feil, ta kontakt med administrator for nettsiden."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment acquirer configuration."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid ""
"<strong>Warning</strong> Creating a payment acquirer from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure your are logged in as the right partner "
"before making this payment."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "Det kreves en token for å opprette en ny betalingstransaksjon."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated (%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token_name)s (%(acq_name)s)."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__access_token
msgid "Access Token"
msgstr "Tilgangstoken"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "Konto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Kontonummer"

#. module: payment
#: code:addons/payment/models/account_payment_method.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
#, python-format
msgid "Acquirer"
msgstr "Innløser"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr "Innløserkonto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr "Innløserreferanse"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr "Innløsere"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Acquirers list"
msgstr "Innløserliste"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Activate"
msgstr "Aktiver"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Activate Stripe"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Aktiv"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr "Legg til ekstra gebyrer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "Adresse"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "Beløp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Maksbeløp"

#. module: payment
#. openerp-web
#: code:addons/payment/controllers/portal.py:0
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "An error occurred during the processing of this payment."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "Bruk"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "Arkivert"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Are you sure you want to delete this payment method?"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Er du sikker på at du vil kansellere den autoriserte transaksjonen? Denne "
"handlingen kan ikke angres."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_authorization
msgid "Authorize Mechanism Supported"
msgstr "Autoriseringsmekanisme støttes"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__auth_msg
msgid "Authorize Message"
msgstr "Melding ved autorisert"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
msgid "Authorize.net"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Autorisert"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Autoriserte transaksjoner"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Availability"
msgstr "Lagerstatus"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "Bank"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Bank Accounts"
msgstr "Bankkonti"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Banknavn"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "Callback Dokument Modell"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_is_done
msgid "Callback Done"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "Callback Hash"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "Callback Metode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Record ID"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "Kanseller"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Avbrutt"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Canceled Message"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Cancelled payments"
msgstr "Avbrutte betalinger"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr "Belast beløp manuelt"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "Belast beløpet"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr "Huk av her"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Velg en betalingsmetode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "Sted"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr "Klikk her for å se bekreftelsen."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid "Close"
msgstr "Lukk"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "Kode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__color
msgid "Color"
msgstr "Farge"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Communication"
msgstr "Kommunikasjon"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "Firma"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Configure"
msgstr "Konfigurer"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "Bekreft sletting"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "Bekreftet"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr "Tilsvarende modul"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "Land"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "Land"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr "Opprett ny betalingsinnløser"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "Create a new payment token"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr "Opprett et betalingsikon"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Credentials"
msgstr "Påloggingsdetaljer"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_stripe
msgid "Credit & Debit Card"
msgstr "Kredit og Debit kort"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_adyen
msgid "Credit Card (powered by Adyen)"
msgstr "Kredittkort (drevet av Ayden)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_alipay
msgid "Credit Card (powered by Alipay)"
msgstr "Kredittkort (drevet av Alipay)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_authorize
msgid "Credit Card (powered by Authorize)"
msgstr "Kredittkort (drevet av Authorize)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_buckaroo
msgid "Credit Card (powered by Buckaroo)"
msgstr "Kredittkort (drevet av Buckaroo)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_ogone
msgid "Credit Card (powered by Ogone)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payulatam
msgid "Credit Card (powered by PayU Latam)"
msgstr "Kredittkort (drevet av PayU Latam)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payumoney
msgid "Credit Card (powered by PayUmoney)"
msgstr "Kredittkort (drevet av PayUmoney)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_sips
msgid "Credit Card (powered by Sips)"
msgstr "Kredittkort (drevet av Sips)"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr "Kreditkort (via Stripe)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Tilpasset betalingsinstruksjon"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Define the display order"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "Beskrivelse"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__display_as
msgid "Description of the acquirer for customers"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Disabled"
msgstr "Deaktivert"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Dismiss"
msgstr "Avvis"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_as
msgid "Displayed as"
msgstr "Vises som"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__done
msgid "Done"
msgstr "Fullført"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr "Melding ved fullført"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Utkast"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Email"
msgstr "E-post"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Enable credit &amp; debit card payments supported by Stripe"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__enabled
msgid "Enabled"
msgstr "Aktivert"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Feil"

#. module: payment
#. openerp-web
#: code:addons/payment/models/payment_transaction.py:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Error: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Fees"
msgstr "Gebyrer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_fees_computation
msgid "Fees Computation Supported"
msgstr "Større for utregning av gebyrer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Faste gebyrer innland"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Faste gebyrer utland"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__acquirer_id
msgid "Force Payment Acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_link_wizard__acquirer_id
msgid ""
"Force the customer to pay via the specified payment acquirer. Leave empty to"
" allow the customer to choose among all acquirers."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "Fra"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__full_only
msgid "Full Only"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Generer link til betaling"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Generer betalingslink"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Generer link til betaling"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "Grupper etter"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-ruting"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__has_multiple_acquirers
msgid "Has Multiple Acquirers"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr "Hjelpemelding"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__new_user
msgid "I don't have a Paypal account"
msgstr "Jeg har ikke en PayPal-konto"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__existing_user
msgid "I have a Paypal account"
msgstr "Jeg har en PayPal-konto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "If not defined, the acquirer name will be used."
msgstr "Hvis ikke definert, vil innløsernavnet brukes."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "Hvis betalingen ikke er bekreftet, kan du kontakte oss."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_128
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "Bilde"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr "Bilde som vises på betalingsskjemaet"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,help:payment.field_payment_acquirer__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the acquirer."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inline_form_view_id
msgid "Inline Form Template"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Install"
msgstr "Installer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr "Installasjonsstatus"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Installed"
msgstr "Installert"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "Intern serverfeil"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "Fakturaer"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Fakturaer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "It is currently linked to the following documents:"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: payment
#: model:ir.model,name:payment.model_account_move
msgid "Journal Entry"
msgstr "Bilag"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__just_done
msgid "Just done"
msgstr "Nettopp ferdig"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Språk"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Leave empty to allow all acquirers"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage payment methods"
msgstr "Administrer betalingsmetoder"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Manuell"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "Merchant Konto-ID"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "Melding"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Messages"
msgstr "Meldinger"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Metode"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_mollie
msgid "Mollie"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Multiple payment options selected"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Name"
msgstr "Navn"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__provider__none
msgid "No Provider Set"
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Acquirer menu."
msgstr ""
"Ingen manuelle betalingsmetoder ble funnet for dette firmaet. Opprett en fra"
" menyen for betalingsinnløsere."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "No payment has been processed."
msgstr "Ingen betalinger gjennomført."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "No payment option selected"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__not_done
msgid "Not done"
msgstr "Ikke fullført"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "Not verified"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from acquirers allowing to capture the amount are "
"available."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Merk at tokener fra innløsere, som er satt til å kun autorisere "
"transaksjoner (istedenfor å belaste beløpet), ikke er tilgjengelig."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Odoo Enterprise Modul"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online Payments"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online payments enabled"
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "Kun administratorer har tilgang til disse dataene."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be captured."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be voided."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only confirmed transactions can be refunded."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "Operasjon"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Annen"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "Annen betalingsinnløser"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT Identitetstoken"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__partial
msgid "Partial"
msgstr "Delvis"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "Partner"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Partnernavn"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Pay"
msgstr "Betal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "Betaling"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Acquirer"
msgstr "Betalingsinnløser"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_list
msgid "Payment Acquirers"
msgstr "Betalingsinnløsere"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__available_acquirer_ids
msgid "Payment Acquirers Available"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "Betalingsbeløp"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Followup"
msgstr "Betalingsoppfølging"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Form"
msgstr "Betalingsskjema"

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Payment Icon"
msgstr "Betalingsikon"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr "Betalingsikoner"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Betalingsinnstruksjon"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "Betalingsjournal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Betalingslink"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Betalingsmetode"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_method_line
msgid "Payment Methods"
msgstr "Betalingsmetoder"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__description
msgid "Payment Ref"
msgstr "Betalingsreferanse"

#. module: payment
#: model:ir.model,name:payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "Betalingstoken"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model:ir.ui.menu,name:payment.payment_token_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "Betalingstokener"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "Betalingstransaksjon"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "Betalingstransaksjoner"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr "Payment acquire onboarding wizard"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__payment_acquirer_selection
msgid "Payment acquirer selected"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "Betalinger"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments failed"
msgstr "Betalinger feilet"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments received"
msgstr "Betalinger mottatt"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "PayPal brukertype"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "Venter"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr "Melding ved avventende"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Telefon"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select a payment option."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select only one payment option."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount smaller than %s."
msgstr "Sett et beløp mindre enn %s."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "Please switch to company '%s' to make this payment."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr "Vennligst bruk følgende overføringsdetaljer"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr "Vær vennlig å bruke ordrenavn som kommunikasjonsreferanse."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Please wait ..."
msgstr "Vennligst vent..."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Behandlet av"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_token__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Provider"
msgstr "Transportør"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Reason:"
msgstr "Årsak:"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Reason: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__redirect_form_view_id
msgid "Redirect Form Template"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "Referanse"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "Referanse må være unik!"

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "Kreditnota"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "Tilbakebetaling."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__refunds_count
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_payment_register
msgid "Register Payment"
msgstr "Registrer betaling"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "Tilknyttet dokument-ID"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Tilknyttet dokumentmodell"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "SEPA Direkte-debitering"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_account_journal_form
msgid "SETUP"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "Save Payment Method"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Save my payment details"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "Lagrede betalingstokener"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Select countries. Leave empty to use everywhere."
msgstr "Velg land. La stå tomt for å bruke overalt."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
#: model:ir.model.fields,field_description:payment.field_payment_icon__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Server Error"
msgstr "Serverfeil"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Server error:"
msgstr "Serverfeil:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_allow_tokenization
msgid "Show Allow Tokenization"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_auth_msg
msgid "Show Auth Msg"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_cancel_msg
msgid "Show Cancel Msg"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_credentials_page
msgid "Show Credentials Page"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_done_msg
msgid "Show Done Msg"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_payment_icon_ids
msgid "Show Payment Icon"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pending_msg
msgid "Show Pending Msg"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pre_msg
msgid "Show Pre Msg"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "Modus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "Status"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Stripe publiserbar nøkkel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe hemmelig nøkkel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr "Støttede betalingsikoner"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,help:payment.field_account_payment_register__use_electronic_payment_method
msgid "Technical field used to hide or show the payment_token_id if needed."
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_test
msgid "Test"
msgstr "Test"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__test
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.manage
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Test Mode"
msgstr "Testmodus"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__provider
#: model:ir.model.fields,help:payment.field_payment_token__provider
#: model:ir.model.fields,help:payment.field_payment_transaction__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__acquirer_ref
msgid "The acquirer reference of the token of the transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "The acquirer reference of the transaction"
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "The anonymized acquirer reference of the payment method"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__color
msgid "The color of the card in kanban view"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"The countries for which this payment acquirer is available.\n"
"If none is set, it is available for all countries."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__description
msgid "The description shown in the card in kanban view "
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "The fees amount; set by the system as it depends on the acquirer"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The following fields must be filled: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid "The journal in which the successful transactions are posted"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "The list of acquirers supporting this payment icon"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__auth_msg
msgid "The message displayed if payment is authorized"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid ""
"The message displayed if the order is canceled during the payment process"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "The related payment is posted: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of related refund transactions"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is canceled "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is pending "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr "Beløpet for betalingen må være positivt."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"acquirer's database, allowing the customer to reuse it for a next purchase."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
#: model:ir.model.fields,help:payment.field_payment_icon__image_payment_form
msgid ""
"This field holds the image used for this payment icon, limited to 64x64 px"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment "
"acquirers. Setting an email for this partner is advised."
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "This payment has not been processed yet."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has been verified by our system."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has not been verified by our system."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "This transaction has been cancelled."
msgstr "Denne transaksjonen er kansellert."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_tokenization
msgid "Tokenization Supported"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Transaction authorization is not supported by the following payment "
"acquirers: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "Transaksjoner"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Unable to contact the Odoo server."
msgstr "Kan ikke kontakte Odoo-serveren."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Upgrade"
msgstr "Oppgrader"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Varible gebyrer innland (i prosent)"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Variable fees must always be positive and below 100%."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Variable gebyrer utland (i prosent)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr "Verifisert"

#. module: payment
#: model:ir.model,name:payment.model_ir_ui_view
msgid "View"
msgstr "Vis"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "Annuller transaksjon"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Waiting for payment"
msgstr "Venter på betaling"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Warning!"
msgstr "Advarsel!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to delete your payment method."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr "Vi kan ikke finne din betaling, men fortvil ikke."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to save your payment method."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/post_processing.js:0
#, python-format
msgid "We are processing your payment, please wait ..."
msgstr "Vi behandler din betaling, vennligst vent..."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr "Vi venter på at betalingsinnløseren skal bekrefte betalingen."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__callback_is_done
msgid "Whether the callback has already been executed"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
msgid "Wire Transfer"
msgstr "Bankoverføring"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr "Du kan klikke her for å gå videre til bekreftelse."

#. module: payment
#: code:addons/payment/models/account_journal.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to an acquirer in the enabled or test state.\n"
"Linked acquirer(s): %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/ir_ui_view.py:0
#, python-format
msgid "You cannot delete a view that is used by a payment acquirer."
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "You do not have access to this payment token."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr "Du vil innen få minutter, motta en e-post som bekrefter din betaling."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr "Du vil få beskjed når betalingen er bekreftet."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr "Du vil få beskjed når betalingen er bekreftet."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order has been processed."
msgstr "Din ordre er behandlet."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr "Din ordre blir behandlet, vennligst vent..."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been authorized."
msgstr "Din betaling er autorisert."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been cancelled."
msgstr "Din betaling er kansellert."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr "Din betaling er mottatt, men må bekreftes manuelt."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_test
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "Din betaling er behandlet, men venter på godkjenning."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been successfully processed. Thank you!"
msgstr "Din betaling er behandlet. Takk skal du ha!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment is in pending state."
msgstr "Din betaling venter."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "Postnummer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "Postnummer"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "payment: post-process transactions"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show less"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show more"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr "for å velge en annen betalingsmetode."
