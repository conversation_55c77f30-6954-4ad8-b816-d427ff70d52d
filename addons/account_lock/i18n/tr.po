# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"Herhangi bir yeni Genel Kullanıcı Kilitleme Tarihi, bir öncekinin arkasında "
"(veya ona eşit) olmalıdır."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr "Müşavirler için kilit tarihleri geri alınamaz ve değiştirilemez."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"Yeni vergi kilidi tarihi, önceki kilit tarihinden sonraya ayarlanmalıdır."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "Vergi kilidi tarihi geri alınamaz ve kaldırılamaz."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Henüz bitmemiş bir dönemi kilitleyemezsiniz. Bu nedenle, Genel Kilit Tarihi,"
" önceki ayın son gününden önce (veya eşit) olmalıdır."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Henüz bitmemiş bir dönemi kilitleyemezsiniz. Bu nedenle, Vergi kilit tarihi "
"bir önceki ayın son gününden önce (veya eşit) olmalıdır."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on advisors than on users. Therefore, "
"the All Users Lock Date must be anterior (or equal) to the Invoice/Bills "
"Lock Date."
msgstr ""
"Danışmanlar için kullanıcılardan daha katı kısıtlamalar ayarlayamazsınız. Bu"
" nedenle, Genel Kilit Tarihi, Fatura/Fatura Kilitleme Tarihi'nin önünde "
"(veya eşit) olmalıdır."
