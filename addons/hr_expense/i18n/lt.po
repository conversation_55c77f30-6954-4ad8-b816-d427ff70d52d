# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
# Translators:
# <PERSON><PERSON><PERSON> <vait<PERSON><EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>. <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Silvija <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <edgara<PERSON>@focusate.eu>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>tolij, 2022
# <PERSON><PERSON><PERSON><PERSON>na <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# Martin Trigaux, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Jonas Zinkevicius <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr ""

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<i class=\"text-muted oe_edit_only\">Use this reference as a subject prefix "
"when submitting by email.</i>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Approve the report here.</p><p>Tip: if you refuse, don’t forget to give "
"the reason thanks to the hereunder message tool</p>"
msgstr ""
"<p>Čia patvirtinkite ataskaitą.</p><p>Patarimas: jei atmesite, nepamirškite "
"pateikti priežasties apačioje esančiu žinučių įrankiu</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Click on <b> Action Create Report </b> to submit selected expenses to "
"your manager</p>"
msgstr ""
"<p>Paspauskite<b>\"Ataskaitos kūrimo veiksmas</b>, kad pateiktumėte "
"pasirinktas išlaidas savo vadovui</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Click on <b> Create Report </b> to create the report.</p>"
msgstr "<p>Paspauskite <b>\"Kurti ataskaitą\"</b>, kad sukurtumėte ataskaitą.</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Once your <b> Expense </b> is ready, you can save it.</p>"
msgstr "<p>Kai jūsų<b>išlaidos</b>yra paruoštos, galite jas išsaugoti.</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Select expenses to submit them to your manager</p>"
msgstr "<p>Pasirinkite išlaidas, kurias pateiksite savo vadovui</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>The accountant receive approved expense reports.</p><p>He can post "
"journal entries in one click if taxes and accounts are right.</p>"
msgstr ""
"<p>Apskaitininkas gauna patvirtintas išlaidų ataskaitas.</p><p>Jis gali "
"paskelbti žurnalo įrašus vienu paspaudimu, jei mokesčiai ir sąskaitos yra "
"teisingi.</p>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Date:</strong>"
msgstr "<strong>Data:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Description:</strong>"
msgstr "<strong>Aprašymas:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Employee:</strong>"
msgstr "<strong>Darbuotojas:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Payment By:</strong>"
msgstr "<strong>Mokėjimas pagal:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Total</strong>"
msgstr "<strong>Suma</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Validated By:</strong>"
msgstr "<strong>Patvirtino:</strong>"

#. module: hr_expense
#: model:product.product,name:hr_expense.accomodation_expense_product
#: model:product.template,name:hr_expense.accomodation_expense_product_product_template
msgid "Accomodation"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "Sąskaita"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Accounting"
msgstr "Apskaita"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Accounting Date"
msgstr "Apskaitos data "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "Reikalingas veiksmas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "Veiklos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos Išimties Dekoravimas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "Veiklos būsena"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "Veiklos tipo ikona"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "Veiklos tipai"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr "Administratorius"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr "Pseudonimas"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "Visos išlaidų ataskaitos"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
msgid "All My Expenses"
msgstr "Visos mano išlaidos"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all
msgid "All Reports"
msgstr "Visos ataskaitos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__amount_residual
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr "Mokėtina suma"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/upload_mixin.js:0
#, python-format
msgid "An error occurred during the upload"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "Tikimasi išlaidų sąskaitos"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "An expense of same category, amount and date already exists."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "An expense report must contain only lines from the same company."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_account_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Analytic Account"
msgstr "Analitinė sąskaita"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Analitinės žymos"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Apple App Store"
msgstr "Apple App Store"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr "Patvirtinimo data"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr "Patvirtinti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Approve Report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
msgid "Approve the new expense reports submitted by the employees you manage."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "Patvirtinta"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr "Patvirtinęs asmuo"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Approved Expenses"
msgstr "Patvirtintos išlaidos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr "Patvirtinimo data"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr "Archyvuotas"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Attach Receipt"
msgstr "Prisiegti kvitą"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Attach your receipt here."
msgstr "Čia prisekite kvitą."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "Bank Journal"
msgstr "Banko žurnalas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__reference
msgid "Bill Reference"
msgstr "Sąskaitos numeris"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr "Gali patvirtinti"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr "Gali anuliuoti"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
msgid "Can be Expensed"
msgstr "Gali būti naudojamas išlaidoms"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#, python-format
msgid "Cancel"
msgstr "Atšaukti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Category"
msgstr "Kategorija"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr "Kategorija:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr "Patvirtintas įsipareigojimas, <br/>(data ir parašas).<br/><br/>"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr "Įmonė"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "Konfigūracija"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Confirmed Expenses"
msgstr "Patvirtintos išlaidos"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konvertavimas galimas tik tarp tai pačiai grupei priklausančių matavimo "
"vienetų. Konvertuojant kiekiai bus paskaičiuoti pagal santykį."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create"
msgstr "Sukurti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Create Report"
msgstr "Sukurti ataskaitą"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr "Sukurkite naują išlaidų ataskaitą"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr "Kurti išlaidas iš gautų el. laiškų"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr "Sukurkite naujas išlaidas, kad matytumėte statistikos duomenis"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create record"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
msgid "Currency"
msgstr "Valiuta"

#. module: hr_expense
#: model:product.product,name:hr_expense.allowance_expense_product
#: model:product.template,name:hr_expense.allowance_expense_product_product_template
msgid "Daily Allowance"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr "Data"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.allowance_expense_product
#: model:product.template,uom_name:hr_expense.allowance_expense_product_product_template
msgid "Days"
msgstr "Dienos"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr "Gerb."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "Numatytasis pseudonimas išlaidoms"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr "Skyrius"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
msgid "Description"
msgstr "Aprašymas"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Did you try the mobile app?"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Documents"
msgstr "Dokumentai"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr "Atlikta"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
msgid "Draft"
msgstr "Juodraštis"

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
#, python-format
msgid "Duplicate Expense"
msgstr "Dubliuoti išlaidą"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr "Darbuotojas"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr "Darbuotojas (kompensavimui)"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr "Darbuotojų išlaidos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__address_id
msgid "Employee Home Address"
msgstr "Darbuotojo namų adresas"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "Išlaida"

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr "Išlaidų patvirtinimas"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr "Išlaidos patvirtintojas(-a)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr "Išlaidų data"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr "Kvito skaitmenizavimas (OCR)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr "Išlaidų žurnalas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "Išlaidų eilutės"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_editable
msgid "Expense Lines Are Editable By Current User"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Products"
msgstr "Išlaidų produktai"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr "Išlaidų atmetimo priežasties vedlys"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr "Išlaidų ataskaita"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__state
msgid "Expense Report State"
msgstr "Išlaidų ataskaitos būsena"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "Išlaidų ataskaitos santrauka"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "Išlaidų ataskaitos"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "Išlaidų ataskaitų analizė"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_pay
msgid "Expense Reports To Pay"
msgstr "Išlaidų ataskaitos apmokėjimui"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_post
msgid "Expense Reports To Post"
msgstr "Išlaidų ataskaitos registravimui"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_approve
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "Išlaidų ataskaitos, kurias reikia patvirtinti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense products can be reinvoiced to your customers."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "Išlaidų atmetimo priežastis"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr "Išlaidų ataskaita patvirtinta"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "Išlaidų ataskaita apmokėta"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "Išlaidų ataskaita atmesta"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_account_move_line__expense_id
msgid "Expense where the move line come from"
msgstr "Išlaidos, iš kurių atėjo perkėlimo eilutė"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model:product.product,name:hr_expense.product_product_fixed_cost
#: model:product.template,name:hr_expense.product_product_fixed_cost_product_template
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expenses"
msgstr "Išlaidos"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "Išlaidų analizė"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "Išlaidų ataskaita"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "Išlaidų ataskaitos, kurias reikia patvirtinti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr "Išlaidos pagal datą"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expenses must be paid by the same entity (Company or employee)."
msgstr ""
"Išlaidos turi būti apmokėtos to paties vieneto (įmonės ar darbuotojo)."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr "Jūsų komandos nario išlaidos"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses to Invoice"
msgstr "Išlaidos, pagal kurias pateikti sąskaitą"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.account_journal_dashboard_kanban_view_inherit_hr_expense
msgid "Expenses to Process"
msgstr "Išlaidos apdorojimui"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_refused
msgid "Explicitly Refused by manager or accountant"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.trans_expense_product
#: model:product.template,name:hr_expense.trans_expense_product_product_template
msgid "Flights, train, bus, taxi, parking"
msgstr "Skrydžiai, Traukiniai, Autobusai, Taksi, Stovėjimo aikštelių išlaidos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome piktograma, pvz., fa-tasks"

#. module: hr_expense
#: model:product.product,name:hr_expense.food_expense_product
#: model:product.template,name:hr_expense.food_expense_product_product_template
msgid "Food & beverages"
msgstr "Maisto ir Gėrimų išlaidos"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr "Buvę darbuotojai"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr "Būsimos veiklos"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr "Bendra informacija"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Generated Expenses"
msgstr "Sukurtos išlaidos"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr "Tvarkykite įrašus su skirtingomis valiutomis"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr "Turi žinutę"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_ids
msgid "Hr Expense"
msgstr "Žmogiškųjų išteklių išlaidos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_sheet_id
msgid "Hr Expense Sheet"
msgstr "Žmogiškųjų išlaidų lapas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
msgid "ID"
msgstr "ID"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr "Piktograma"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Išimties veiklą žyminti piktograma."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__in_payment
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr "Mokėjime"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Included in price taxes"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr "Įeinantys el. laiškai"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Invalid attachments!"
msgstr "Netinkami prisegti failai!"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__invoicing_legacy
msgid "Invoicing App Legacy"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__same_currency
msgid "Is currency_id different from the company_currency_id"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
msgid "Is product with non zero cost selected"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_journal
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr "Žurnalas"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Journal Entry"
msgstr "Žurnalo įrašas"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "Žurnalo įrašas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_convert_rate
msgid "Label Convert Rate"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_total_amount_company
msgid "Label Total Amount Company"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr "Vėluojančios veiklos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__use_mailgateway
msgid "Let your employees record expenses by email"
msgstr "Leiskite savo darbuotojams sekti savo išlaidas el. paštu"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr "Vadovas"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Managers can get all reports to approve from this menu."
msgstr "Vadovai šiame meniu gali matyti visas ataskaitas patvirtinimui."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: hr_expense
#: model:product.product,name:hr_expense.mileage_expense_product
#: model:product.template,name:hr_expense.mileage_expense_product_product_template
msgid "Mileage"
msgstr "Keliavimo išlaidos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Veiklos paskutinis terminas"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr "Mano išlaidos"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_unsubmitted
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_to_submit
msgid "My Expenses to Report"
msgstr "Mano išlaidos pateikimui"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr "Mano ataskaitos"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr "Mano komanda"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "Pavadinimas"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Report"
msgstr "Nauja išlaidų ataskaita"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kito veiksmo terminas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "Kito veiksmo santrauka"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "Kito veiksmo tipas"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"No Expense account found for the product %s (or for its category), please "
"configure one."
msgstr ""
"Produktui %s (ar jo kategorijai) nerasta išlaidų sąskaita, nustatykite ją."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr "Nerastas darbuotojo %s namų adresas, nustatykite jį."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No attachment was provided"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr "Jokių duomenų!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense products found. Let's create one!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid "No expense reports found"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__not_paid
msgid "Not Paid"
msgstr "Neapmokėta"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "Pastabos..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_number
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_number
msgid "Number of Attachments"
msgstr "Prisegtukų kiekis"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Žinučių, kurioms reikia jūsų veiksmo, skaičius"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Number of unread messages"
msgstr "Neperskaitytų žinučių skaičius"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr "Odoo"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""
"Kai sukursite savo išlaidas, pateikite jas savo vadovui, kuris jas "
"patvirtins."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Once your <b>Expense report</b> is ready, you can submit it to your manager "
"and wait for the approval from your manager."
msgstr ""
"Kai jūsų <b>Išlaidų ataskaita</b> bus paruošta, galėsite pateikti ją savo "
"vadovui ir laukti jo patvirtinimo."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""
"Tik personalo skyriaus darbuotojai arba susijęs darbuotojas gali atstatyti į"
" juodraštį."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only Managers and HR Officers can approve expenses"
msgstr ""
"Tik personalo skyriaus darbuotojai arba susijęs darbuotojas gali patvirtinti"
" išlaidas"

#. module: hr_expense
#: model:product.product,name:hr_expense.other_expense_product
#: model:product.template,name:hr_expense.other_expense_product_product_template
msgid "Other expenses"
msgstr "Kitos išlaidos"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__paid
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr "Apmokėta"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr "Apmokėjo"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr "Dalinis"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__partial
msgid "Partially Paid"
msgstr "Dalinai apmokėta"

#. module: hr_expense
#: code:addons/hr_expense/models/account_move.py:0
#, python-format
msgid "Payment Cancelled"
msgstr "Mokėjimas atšauktas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr "Mokėjimo statusas"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Please configure Default Expense account for Category expense: "
"`property_account_expense_categ_id`."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Post Entries"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "Registruoti žurnalų įrašus"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid ""
"Post the journal entries of the new expense reports approved by the "
"employees' manager."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr "Registruotas"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr "Technologija"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price"
msgstr "Kaina"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price in Company Currency"
msgstr "Kaina įmonės valiuta"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr "Kaina:"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
msgid "Product"
msgstr "Produktas"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "Produkto pavadinimas"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
msgid "Product Template"
msgstr "Produkto šablonas"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr "Produkto variantai"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Qty"
msgstr "Kiek."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
msgid "Quantity"
msgstr "Kiekis"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
msgid "Reason"
msgstr "Priežastis"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason :"
msgstr "Priežastis:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr "Išlaidų atmetimo priežastis"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Receipts"
msgstr "Kvitai"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Ref."
msgstr "Nr."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_ref_editable
msgid "Reference Is Editable By Current User"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "Atmesti"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "Atmesti išlaidas"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr "Atmesta"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr "Atmestos išlaidos"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
#, python-format
msgid "Register Payment"
msgstr "Užregistruoti mokėjimą"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr "Kompensuoti išlaidas algalapyje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse expenses in payslips"
msgstr "Kompensuoti išlaidas algalapyje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
msgid ""
"Reimburse the employees who incurred these costs or simply register the "
"corresponding payments."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Report"
msgstr "Ataskaita"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
msgid "Report Company Currency"
msgstr "Įmonės valiutos ataskaita"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "Ataskaitos"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_approve
msgid "Reports to Approve"
msgstr "Ataskaitos, kurias reikia patvirtinti"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_pay
msgid "Reports to Pay"
msgstr "Ataskaitos apmokėjimui"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_post
msgid "Reports to Post"
msgstr "Ataskaitos paskelbimui"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "Atkurti kaip juodraštį"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas vartotojas"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__reversed
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reversed"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS pristatymo klaida"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sample
msgid "Sample"
msgstr "Pavyzdis"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "Save Anyways"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Scan"
msgstr "Skenuoti"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/expense_qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Odoo app:"
msgstr "Skenuokite šį QR kodą, tam kad įsidiegti Odoo:"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Selected Unit of Measure for expense %(expense)s does not belong to the same"
" category as the Unit of Measure of product %(product)s."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "Nustatymai"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your domain alias"
msgstr "Nustatykite savo domeno pseudinimą"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
msgid "Sheet"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Rodyti visus įrašus, kurių sekančio veiksmo data yra ankstesnė nei šiandiena"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid ""
"Snap pictures of your receipts and let Odoo<br> automatically create "
"expenses for you."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Specify expense journal to generate accounting entries."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr "Nurodykite, ar produktas gali būti pasirinktas kaip išlaida."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr "Būsena"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Būsena, paremta veiklomis\n"
"Vėluojantis: Termino data jau praėjo\n"
"Šiandien: Veikla turi būti baigta šiandien\n"
"Suplanuotas: Ateities veiklos."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__state
msgid "Status of the expense."
msgstr "Išlaidos būsena."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Submit to Manager"
msgstr "Pateikti vadovui"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr "Pateikta"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount
msgid "Subtotal"
msgstr "Tarpinė suma"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Taxes"
msgstr "Mokesčiai"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"The accountant can register a payment to reimburse the employee directly."
msgstr ""
"Apskaitininkas gali registruoti mokėjimą ir išlaidas kompensuoti tiesiogiai."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The current user has no related employee. Please, create one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The expense reports were successfully approved."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__journal_id
msgid "The journal used when the expense is done."
msgstr "DK įrašų registravimui naudojamas apskaitos žurnalas."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr "Naudojamas mokėjimo būdas, kai išlaidos apmokamos įmonės."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_ids
msgid "The taxes should be \"Included In Price\""
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "There are no expense reports to approve."
msgstr ""

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Approve"
msgstr "Reikia patvirtinti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Pay"
msgstr "Apmokėjimui"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Post"
msgstr "Paskelbimui"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr "Pateikimui"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
msgid "To Submit"
msgstr "Pateikimui"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr "Šiandienos veiklos"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total"
msgstr "Suma"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total %s"
msgstr "Suma %s"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "Visa suma"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Company Currency"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
msgid "Total In Currency"
msgstr "Suma einamąja valiuta"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "Vieneto kaina"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "Mato vienetas"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.accomodation_expense_product
#: model:product.product,uom_name:hr_expense.food_expense_product
#: model:product.product,uom_name:hr_expense.other_expense_product
#: model:product.product,uom_name:hr_expense.product_product_fixed_cost
#: model:product.product,uom_name:hr_expense.trans_expense_product
#: model:product.template,uom_name:hr_expense.accomodation_expense_product_product_template
#: model:product.template,uom_name:hr_expense.food_expense_product_product_template
#: model:product.template,uom_name:hr_expense.other_expense_product_product_template
#: model:product.template,uom_name:hr_expense.product_product_fixed_cost_product_template
#: model:product.template,uom_name:hr_expense.trans_expense_product_product_template
msgid "Units"
msgstr "Vienetai"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread
msgid "Unread Messages"
msgstr "Neperskaitytos žinutės"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neperskaitytų žinučių skaičiavimas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Upload"
msgstr "Įkelti"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "Users"
msgstr "Vartotojai"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "Rodyti prisegtukus"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr "Peržiūrėti išlaidą"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "Rodyti ataskaitą"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Want to manage your expenses? It starts here."
msgstr "Norite valdyti savo išlaidas? Viskas prasideda čia."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "Interneto svetainės žinutės"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr "Svetainės komunikacijos istorija"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user using"
" this product won't be able to change the amount of the expense, only the "
"quantity. Use a cost different than 0 for expense categories funded by the "
"company at fixed cost like allowances for mileage, per diem, accomodation or"
" meal."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit the reference of this expense report."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit this expense report."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can not create report without category."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only approve your department expenses"
msgstr "Galite patvirtinti tik savo skyriaus išlaidas"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr "Jūs galite sukurti apskaitos įrašą tik patvirtintoms išlaidoms."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only refuse your department expenses"
msgstr "Galite atmesti tik savo skyriaus išlaidas"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can't mix sample expenses and regular ones"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot add expenses of another employee."
msgstr "Negalite pridėti kito darbuotojo išlaidų."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot approve your own expenses"
msgstr "Negalite patvirtinti savo paties išlaidų"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or approved expense."
msgstr "Negalite ištrinti registruotų ar patvirtintų išlaidų."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr "Negalite ištrinti paskelbtų arba apmokėtų išlaidų."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot refuse your own expenses"
msgstr "Negalite atmesti savo paties išlaidų"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different employees in the same report."
msgstr ""
"Negalite toje pačioje ataskaitoje pateikti skirtingų darbuotojų išlaidų."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report twice the same line!"
msgstr "Negalite du kartus pateikti tos pačios eilutės išlaidų!"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense"
msgstr "Jūsų išlaidos"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "pvz. Pietūs"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "pvz. Pietūs su klientu"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "pvz. Kelionė į Niujorką"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "buvo atmesta"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.mileage_expense_product
#: model:product.template,uom_name:hr_expense.mileage_expense_product_product_template
msgid "km"
msgstr "km"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to be reimbursed"
msgstr "Turi būti kompensuoti"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to report"
msgstr "Pateikimui"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "under validation"
msgstr "Laukia patvirtinimo"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr ""
"naudoti nuskaitymą (OCR) iš paveikslėlio, tam kad užpildyti išlaidos formą"
