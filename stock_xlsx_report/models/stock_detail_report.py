# -*- coding: utf-8 -*-
# Copyright  Softprime Consulting Pvt Ltd

from odoo import models, fields


class StockReportDbView(models.Model):
    _name = "stock.detail.report.db.view"
    _description = "Stock Detail Report"

    date = fields.Datetime('Date')
    product_id = fields.Many2one('product.product', 'Product Name')
    reference = fields.Char('Reference')
    src_location_id = fields.Many2one('stock.location', string='From')
    dest_location_id = fields.Many2one('stock.location', string='To')
    opening = fields.Float('Opening')
    purchase = fields.Float('Purchase')
    purchase_return = fields.Float('Purchase Returned')
    sale = fields.Float('Sale')
    sale_return = fields.Float('Sale Returned')
    internal = fields.Float('Internal')
    consume = fields.Float('Consume')
    adjustment = fields.Float('Adjustment')
    at_transit = fields.Float('At Transit')
    scrapped = fields.Float('Scrapped')
    closing = fields.Float('Closing')

    def incoming_outgoing_domain(self, from_date, to_date, warehouse_ids, product_ids, company_id):
        query = " AND sm.company_id = %s" % company_id.id
        if product_ids:
            if len(product_ids) == 1:
                query += " AND sm.product_id = %s" % product_ids.id
            else:
                query += " AND sm.product_id in %s" % str(tuple(product_ids.ids))
        if warehouse_ids:
            if len(warehouse_ids) == 1:
                query += " AND sm.warehouse_id = %s" % warehouse_ids.id
            else:
                query += " AND sm.warehouse_id in %s" % str(tuple(warehouse_ids.ids))
        if from_date:
            query += " AND (sm.date AT TIME ZONE 'UTC' AT TIME ZONE '%s')::date >= '%s'" % (self.env.user.tz, from_date)
        if to_date:
            query += " AND (sm.date AT TIME ZONE 'UTC' AT TIME ZONE '%s')::date <= '%s'" % (self.env.user.tz, to_date)
        return query

    def opening_incoming_outgoing_domain(self, from_date, warehouse_ids, product_ids, company_id):
        query = " AND sm.company_id = %s" % company_id.id
        if product_ids:
            if len(product_ids) == 1:
                query += " AND sm.product_id = %s" % product_ids.id
            else:
                query += " AND sm.product_id in %s" % str(tuple(product_ids.ids))
        if warehouse_ids:
            if len(warehouse_ids) == 1:
                query += " AND sm.warehouse_id = %s" % warehouse_ids.id
            else:
                query += " AND sm.warehouse_id in %s" % str(tuple(warehouse_ids.ids))
        if from_date:
            query += " AND (sm.date AT TIME ZONE 'UTC' AT TIME ZONE '%s')::date < '%s'" % (self.env.user.tz, from_date)
        return query

    def stock_move_basic_query(self):
        """Stock Move Basic Query"""
        query = "SELECT sm.id from stock_move sm where sm.state = 'done' "
        return query

    def get_basic_query_incoming_outgoing_opening(self, from_date, to_date, warehouse_ids, product_ids,
                                                  company_id, incoming_outgoing=False, opening=False):
        """Get Basic Query for incoming outgoing and opening"""
        stock_move_qry = self.stock_move_basic_query()
        qry_incoming_outgoing = stock_move_qry + self.incoming_outgoing_domain(
            from_date,
            to_date,
            warehouse_ids,
            product_ids,
            company_id)
        self.env.cr.execute(qry_incoming_outgoing)
        result_incoming_outgoing = self.env.cr.fetchall()
        incoming_outgoing_move_ids = [i[0] for i in result_incoming_outgoing]
        qry_opening_incoming_outgoing = stock_move_qry + self.opening_incoming_outgoing_domain(
            from_date,
            warehouse_ids,
            product_ids,
            company_id)
        self.env.cr.execute(qry_opening_incoming_outgoing)
        opening_result_incoming_outgoing = self.env.cr.fetchall()
        opening_incoming_outgoing_move_ids = [i[0] for i in opening_result_incoming_outgoing]
        if incoming_outgoing:
            return incoming_outgoing_move_ids
        if opening:
            return opening_incoming_outgoing_move_ids

    def get_total_report_details(self, from_date, to_date, warehouse_ids, location_ids, product_ids, company_id):
        """Get Total Report Details One"""
        stock_move_obj = self.env['stock.move']
        product_obj = self.env['product.product']
        location_obj = self.env['stock.location']
        if not product_ids:
            product_ids = product_obj.search([
                ('detailed_type', '=', 'product'),
                '|', ('company_id', '=', company_id.id),
                ('company_id', '=', False)
            ])
        if not location_ids:
            location_ids = location_obj.search([
                ('company_id', '=', company_id.id),
                ('usage', '=', 'internal')
            ])
        transit_location_ids = location_obj.search([
            ('company_id', '=', company_id.id),
            ('usage', '=', 'transit'),
        ])
        incoming_outgoing_ids = self.get_basic_query_incoming_outgoing_opening(
            from_date, to_date, warehouse_ids, product_ids, company_id, incoming_outgoing=True)
        opening_incoming_outgoing_ids = []
        # Get opening incoming and outgoing move IDs
        if from_date:
            opening_incoming_outgoing_ids = self.get_basic_query_incoming_outgoing_opening(
                from_date, to_date, warehouse_ids, product_ids, company_id, opening=True)
        incoming_outgoing_move_ids = stock_move_obj.browse(incoming_outgoing_ids)
        opening_incoming_outgoing_move_ids = stock_move_obj.browse(opening_incoming_outgoing_ids)
        product_location_qty_list = []
        opening_qty = 0.0
        if opening_incoming_outgoing_ids:
            in_op = opening_incoming_outgoing_move_ids.filtered(lambda x: x.location_dest_id.id in location_ids.ids)
            out_op = opening_incoming_outgoing_move_ids.filtered(lambda x: x.location_id.id in location_ids.ids)
            opening_qty = sum(in_op.mapped('product_qty'))
            opening_qty -= sum(out_op.mapped('product_qty'))
        closing = opening_qty
        vals_opening = {
            'date': False,
            'product_id': product_ids.id,
            'reference': "Opening",
            'src_location_id': False,
            'dest_location_id': False,
            'opening': opening_qty,
            'purchase': 0.0,
            'purchase_return': 0.0,
            'sale': 0.0,
            'sale_return': 0.0,
            'internal': 0.0,
            'consume': 0.0,
            'adjustment': 0.0,
            'at_transit': 0.0,
            'scrapped': 0.0,
            'closing': closing,
        }
        product_location_qty_list.append(vals_opening)
        loc_ids = location_ids + transit_location_ids
        for location in loc_ids:
            location_wise_moves = incoming_outgoing_move_ids.filtered(
                lambda x: x.location_dest_id.id == location.id or x.location_id.id == location.id)
            location_mvs = location_wise_moves.filtered(lambda x: x.location_dest_id.usage == 'internal' or x.location_id.usage == 'internal')
            if location.usage == 'transit':
                location_mvs = location_wise_moves.filtered(lambda x: x.location_dest_id.usage == 'transit' or x.location_id.usage == 'transit')
            sorted_move_incoming_outgoing = sorted(
                location_mvs,
                key=lambda move: (move.product_id.id, move.location_id.id, move.date), reverse=False)
            for mv in sorted_move_incoming_outgoing:
                purchase_qty = 0.0
                purchase_return_qty = 0.0
                sale_qty = 0.0
                sale_return_qty = 0.0
                consume_qty = 0.0
                internal_qty = 0.0
                adjustment_qty = 0.0
                at_transit = 0.0
                scrapped = 0.0
                closing_qty = 0.0
                if location.usage == 'internal':
                    if mv.purchase_line_id and mv.location_dest_id.id == location.id:
                        purchase_qty = mv.product_qty
                        closing_qty += purchase_qty
                    if mv.purchase_line_id and mv.location_id.id == location.id:
                        purchase_return_qty -= mv.product_qty
                        closing_qty += purchase_return_qty
                    if mv.sale_line_id and mv.location_id.id == location.id:
                        sale_qty -= mv.product_qty
                        closing_qty += sale_qty
                    if mv.sale_line_id and mv.location_dest_id.id == location.id:
                        sale_return_qty = mv.product_qty
                        closing_qty += sale_return_qty
                    if mv.is_inventory and mv.location_dest_id.id == location.id:
                        adjustment_qty += mv.product_qty
                        closing_qty += adjustment_qty
                    if mv.is_inventory and mv.location_id.id == location.id:
                        adjustment_qty -= mv.product_qty
                        closing_qty += adjustment_qty
                    if mv.scrapped and mv.location_id.id == location.id:
                        scrapped -= mv.product_qty
                        closing_qty += scrapped
                    if mv.location_dest_id.id == location.id and mv.location_id.usage == 'internal':
                        internal_qty += mv.product_qty
                        closing_qty += internal_qty
                    if mv.location_id.id == location.id and mv.location_dest_id.usage == 'internal':
                        internal_qty -= mv.product_qty
                        closing_qty += internal_qty
                    if mv.location_dest_id.usage == 'transit' and mv.location_id.id == location.id:
                        at_transit -= mv.product_qty
                        closing_qty += at_transit
                    if mv.location_id.usage == 'transit' and mv.location_dest_id.id == location.id:
                        at_transit = mv.product_qty
                        closing_qty += at_transit
                    if mv.location_id.id == location.id and mv.location_dest_id.usage == 'customer' and not mv.scrapped and not mv.sale_line_id and not mv.purchase_line_id:
                        consume_qty -= mv.product_qty
                        closing_qty += consume_qty
                    if mv.location_dest_id.id == location.id and mv.location_id.usage == 'customer' and not mv.scrapped and not mv.sale_line_id and not mv.purchase_line_id:
                        consume_qty += mv.product_qty
                        closing_qty += consume_qty
                if not mv.scrapped and not mv.sale_line_id and not mv.purchase_line_id and location.usage == 'transit':
                    if mv.location_id.id == location.id and mv.location_dest_id.usage == 'customer':
                        consume_qty = mv.product_qty
                        at_transit = mv.product_qty
                    if mv.location_dest_id == location.id and mv.location_id.usage == 'customer':
                        consume_qty = mv.product_qty
                        at_transit = mv.product_qty
                closing = closing + closing_qty
                name = mv.name
                if mv.picking_id:
                    name = mv.picking_id.name
                product_location_qty = {
                    'date': mv.date,
                    'product_id': mv.product_id.id,
                    'reference': name,
                    'src_location_id': mv.location_id.id,
                    'dest_location_id': mv.location_dest_id.id,
                    'opening': 0.0,
                    'purchase': purchase_qty,
                    'purchase_return': purchase_return_qty,
                    'sale': sale_qty,
                    'sale_return': sale_return_qty,
                    'internal': internal_qty,
                    'consume': consume_qty,
                    'adjustment': adjustment_qty,
                    'at_transit': at_transit,
                    'scrapped': scrapped,
                    'closing': closing,
                }
                product_location_qty_list.append(product_location_qty)
        list_field = ['opening', 'purchase', 'purchase_return', 'sale', 'sale_return', 'internal', 'consume',
                      'adjustment', 'at_transit', 'scrapped']
        filtered_data = [item for item in product_location_qty_list if any(item[field] != 0.0 for field in list_field)]
        return filtered_data

    def create_data_records(self, from_date, to_date, warehouse_ids, location_ids, product_ids, company_id):
        """Create Data Record"""
        self.env.cr.execute("""delete from stock_detail_report_db_view""")
        report_values = self.get_total_report_details(from_date, to_date, warehouse_ids, location_ids, product_ids,
                                                      company_id)
        self.env['stock.detail.report.db.view'].create(report_values)
