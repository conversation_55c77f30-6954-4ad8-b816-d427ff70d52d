# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
import base64
import logging
from datetime import datetime
from io import BytesIO

import xlrd
from dateutil.relativedelta import relativedelta
from odoo import _, fields, models
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class ImportAsset(models.TransientModel):
    _name = "import.asset"
    _description = "Import Asset"

    import_sheet = fields.Binary("Import Sheet")
    company_id = fields.Many2one(
        "res.company", "Company", default=lambda self: self.env.company
    )
    opening_import = fields.Boolean("Opening Import", default=True)
    account_id = fields.Many2one("account.account", "Opening Account")
    opening_date = fields.Date("Opening Date")
    start_deprecation_date = fields.Date("Start Deprecation Date")

    # def import_excel_template(self):
    #     """
    #     import excel template for assets
    #     """
    #     asset_model_obj = self.env["account.asset"]
    #     employee_obj = self.env["hr.employee"]
    #     location_obj = self.env["asset.location"]
    #     count = 0
    #     if not self.import_sheet:
    #         raise ValidationError(_("Please add file"))
    #     val = base64.decodestring(self.import_sheet)
    #     file_p = BytesIO()
    #     file_p.write(val)
    #     book = xlrd.open_workbook(file_contents=file_p.getvalue())
    #     sheet_name = book.sheet_by_index(0)
    #     update_list = []
    #     asset_list = []
    #     _logger.info("row count : %d", sheet_name.nrows)
    #     for line in range(0, sheet_name.nrows):
    #         row = sheet_name.row_values(line)
    #         try:
    #             update_list.append(row[0].lower().rstrip(" "))
    #             update_list.append(row[1].lower().rstrip(" "))
    #             update_list.append(row[2].lower().rstrip(" "))
    #             update_list.append(row[3].lower().rstrip(" "))
    #             update_list.append(row[4].lower().rstrip(" "))
    #             update_list.append(row[5].lower().rstrip(" "))
    #             update_list.append(row[6].lower().rstrip(" "))
    #             update_list.append(row[7].lower().rstrip(" "))
    #             update_list.append(row[8].lower().rstrip(" "))
    #             update_list.append(row[9].lower().rstrip(" "))
    #             update_list.append(row[10].lower().rstrip(" "))
    #             update_list.append(row[11].lower().rstrip(" "))
    #             update_list.append(row[12].lower().rstrip(" "))
    #             update_list.append(row[13].lower().rstrip(" "))
    #             update_list.append(row[14].lower().rstrip(" "))
    #             update_list.append(row[15].lower().rstrip(" "))
    #         except Exception:
    #             raise ValidationError(_("Please add Header in Excel sheet "))
    #         try:
    #             asset_name = update_list.index("asset name")
    #             asset_tag_number = update_list.index("asset tag number")
    #             responsible_person = update_list.index("responsible person")
    #             asset_location = update_list.index("asset location")
    #             acquisition_date = update_list.index("acquisition date")
    #             original_value = update_list.index("original value")
    #             already_deprecated_value = update_list.index("already deprecated value")
    #             depriciated_Value = update_list.index("depriciated value")
    #             not_depreciable_value = update_list.index("not depreciable value")
    #             update_list.index("depreciation method")
    #             update_list.index("method number")
    #             first_depreciation_date = update_list.index("first depreciation date")
    #             update_list.index("book value")
    #             status = update_list.index("status")
    #             asset_model = update_list.index("asset model")
    #             break
    #         except Exception:
    #             raise ValidationError(
    #                 _(
    #                     "Import xls file header is not correct!. Please correct it first and then re import !"
    #                 )
    #             )
    #     for line in range(1, sheet_name.nrows):
    #         count += 1
    #         row = sheet_name.row_values(line)
    #         asset_model_id = asset_model_obj.search(
    #             [
    #                 ("name", "=", " ".join(str(row[asset_model]).split())),
    #                 ("asset_type", "=", "purchase"),
    #                 ("state", "=", "model"),
    #             ],
    #             limit=1,
    #         )
    #         asset_tag_already_exit = asset_model_obj.search(
    #             [("asset_sequence", "=", row[asset_tag_number])]
    #         )
    #         hr_id = employee_obj.search(
    #             [("name", "=", " ".join(str(row[responsible_person]).split()))]
    #         )
    #         location_id = location_obj.search(
    #             [("name", "=", " ".join(str(row[asset_location]).split()))]
    #         )
    #         aq_date_type = isinstance(row[acquisition_date], float)
    #         if aq_date_type:
    #             seconds = (row[acquisition_date] - 25569) * 86400.0
    #             date = datetime.utcfromtimestamp(seconds)
    #             acq_date = date
    #         else:
    #             acq_date = datetime.strptime(
    #                 str(row[acquisition_date]), "%m/%d/%Y"
    #             ).date()
    #         dep_date_type = isinstance(row[first_depreciation_date], float)
    #         if dep_date_type:
    #             seconds = (row[first_depreciation_date] - 25569) * 86400.0
    #             date = datetime.utcfromtimestamp(seconds)
    #         else:
    #             datetime.strptime(str(row[first_depreciation_date]), "%m/%d/%Y").date()
    #         if asset_tag_already_exit:
    #             raise ValidationError(_("This Asset tag is already exit !!"))
    #         if not location_id:
    #             raise ValidationError(_("Asset location is not found !!"))
    #         if not hr_id and row[responsible_person]:
    #             raise ValidationError(_("Responsible person is not found !!"))
    #         if not asset_model_id:
    #             raise ValidationError(_("Asset Model is not found !!"))
    #         if row[original_value] < 0:
    #             raise ValidationError(_("Original value is negative !!"))
    #         if row[depriciated_Value] < 0:
    #             raise ValidationError(_("Remaining value to deprecat is negative !!"))
    #         vals = {
    #             "name": row[asset_name],
    #             "responsible_person_id": hr_id.id,
    #             "asset_sequence": row[asset_tag_number],
    #             "asset_location_id": location_id.id,
    #             "acquisition_date": acq_date,
    #             "original_value": round(row[original_value], 2),
    #             "already_depreciated_amount_import": round(
    #                 row[already_deprecated_value], 2
    #             ),
    #             "account_depreciation_id": asset_model_id.account_depreciation_id.id,
    #             "first_depreciation_date": self.start_deprecation_date,
    #             "value_residual": round(row[depriciated_Value], 2),
    #             "salvage_value": round(row[not_depreciable_value], 2),
    #             "state": row[status],
    #             "asset_type": "purchase",
    #             "import_opening_asset": True,
    #             "opening_date": self.opening_date,
    #             "model_id": asset_model_id.id,
    #             "opening_equity_id": self.account_id.id,
    #         }
    #         account_asset_obj = self.env["account.asset"].create(vals)
    #         if account_asset_obj:
    #             delta = relativedelta(self.start_deprecation_date, acq_date)
    #             account_asset_obj.first_depreciation_date = self.start_deprecation_date
    #             asset_list.append(account_asset_obj.id)
    #             account_asset_obj._onchange_model_id()
    #             account_asset_obj.import_opening_asset = True
    #             account_asset_obj.first_depreciation_date = self.start_deprecation_date
    #             if asset_model_id.method_period == "12":
    #                 if delta.months and delta.years > 0:
    #                     remaining_month = asset_model_id.method_number - delta.years
    #                     if remaining_month:
    #                         account_asset_obj.method_number = remaining_month
    #             else:
    #                 if delta.months and delta.months > 0:
    #                     year = delta.years * 12 if delta.years else 0
    #                     remaining_month = asset_model_id.method_number - (year + delta.months)
    #                     if remaining_month:
    #                         account_asset_obj.method_number = remaining_month
    #             account_asset_obj.compute_depreciation_board()
    #     return {
    #         "type": "ir.actions.act_window",
    #         "name": "Asset",
    #         "res_model": "account.asset",
    #         "view_mode": "tree,form",
    #         "context": {"create": False},
    #         "domain": [("id", "in", asset_list)],
    #     }

    def import_excel_template(self):
        """
        Import Excel template for assets.
        """
        asset_model_obj = self.env["account.asset"]
        employee_obj = self.env["hr.employee"]
        location_obj = self.env["asset.location"]
        journal_obj = self.env["account.journal"]
        account_obj = self.env["account.account"]
        asset_list = []

        if not self.import_sheet:
            raise ValidationError(_("Please add a file."))

        val = base64.decodebytes(self.import_sheet)
        file_p = BytesIO()
        file_p.write(val)
        book = xlrd.open_workbook(file_contents=file_p.getvalue())
        sheet = book.sheet_by_index(0)

        # Extract and validate headers (fix typo in first_depreciation_date_import)
        headers = [header.strip().lower() for header in sheet.row_values(0)]
        required_headers = [
            "name",
            "asset_sequence",
            "responsible person",
            "asset location",
            "model_id",
            "acquisition_date",
            "first_depreciation_date",
            "original_value",
            "already_depreciated_amount_import",
            "method_number",
            "method_period",
            "opening_date",
            "depreciation_number_import",
            "first_depreciated_date_import",
            "state",
            "journal_id",
            "account_asset_id",
            "account_depreciation_id",
            "account_depreciation_expense_id",
        ]
        missing = [h for h in required_headers if h not in headers]
        if missing:
            raise ValidationError(_(f"Missing headers: {', '.join(missing)}"))
        col_index = {header: headers.index(header) for header in headers}
        for row_idx in range(1, sheet.nrows):
            try:
                row = sheet.row_values(row_idx)
                asset_name = row[col_index["name"]]
                asset_sequence = row[col_index["asset_sequence"]]
                responsible_person = row[col_index["responsible person"]]
                asset_location = row[col_index["asset location"]]
                model_id = row[col_index["model_id"]]
                acquisition_date = row[col_index["acquisition_date"]]
                first_depreciation_date = row[col_index["first_depreciation_date"]]
                original_value = float(row[col_index["original_value"]])
                already_depreciated_amount = float(row[col_index["already_depreciated_amount_import"]])
                method_number = int(row[col_index["method_number"]])
                method_period = str(row[col_index["method_period"]]).strip().lower()
                state = str(row[col_index["state"]]).strip().lower()
                journal_name = row[col_index["journal_id"]].strip()
                account_asset_name = row[col_index["account_asset_id"]].strip()
                account_depreciation_name = row[col_index["account_depreciation_id"]].strip()
                account_expense_name = row[col_index["account_depreciation_expense_id"]].strip()
                opening_date = row[col_index["opening_date"]]
                depreciation_number_import = row[col_index["depreciation_number_import"]]
                first_depreciation_date_import = row[col_index["first_depreciated_date_import"]]  # Corrected
                journal = journal_obj.search([("name", "=", journal_name)], limit=1)
                if not journal:
                    raise ValidationError(_(f"Journal '{journal_name}' not found (row {row_idx + 1})"))
                account_asset = account_obj.search([("name", "=", account_asset_name)], limit=1)
                account_depreciation = account_obj.search([("name", "=", account_depreciation_name)], limit=1)
                account_expense = account_obj.search([("name", "=", account_expense_name)], limit=1)
                if not account_asset:
                    raise ValidationError(_(f"Account Asset '{account_asset_name}' not found (row {row_idx + 1})"))
                if not account_depreciation:
                    raise ValidationError(
                        _(f"Depreciation Account '{account_depreciation_name}' not found (row {row_idx + 1})"))
                if not account_expense:
                    raise ValidationError(_(f"Expense Account '{account_expense_name}' not found (row {row_idx + 1})"))
                allowed_periods = {"1": "1", "12": "12", "months": "1", "years": "12"}
                if method_period not in allowed_periods:
                    raise ValidationError(_(f"Invalid method period '{method_period}' (row {row_idx + 1})"))
                method_period = allowed_periods[method_period]
                asset_model = asset_model_obj.search([
                    ("name", "=", model_id),
                    ("asset_type", "=", "purchase"),
                    ("state", "=", "model")
                ], limit=1)
                if not asset_model:
                    raise ValidationError(_(f"Model '{model_id}' not found (row {row_idx + 1})"))
                if asset_sequence and asset_model_obj.search([("asset_sequence", "=", asset_sequence)]):
                    raise ValidationError(_(f"Asset tag '{asset_sequence}' already exists (row {row_idx + 1})"))
                responsible_employee = employee_obj.search([("name", "=", responsible_person)], limit=1)
                if not responsible_employee:
                    raise ValidationError(_(f"Employee '{responsible_person}' not found (row {row_idx + 1})"))
                location = location_obj.search([("name", "=", asset_location)], limit=1)
                if not location:
                    raise ValidationError(_(f"Location '{asset_location}' not found (row {row_idx + 1})"))
                try:
                    acq_date = xlrd.xldate.xldate_as_datetime(acquisition_date, book.datemode)
                except TypeError:
                    acq_date = datetime.strptime(acquisition_date, "%Y-%m-%d %H:%M:%S")
                try:
                    dep_date = xlrd.xldate.xldate_as_datetime(first_depreciation_date, book.datemode)
                except TypeError:
                    dep_date = datetime.strptime(first_depreciation_date, "%Y-%m-%d %H:%M:%S")
                try:
                    opening_date = xlrd.xldate.xldate_as_datetime(opening_date, book.datemode)
                except TypeError:
                    opening_date = datetime.strptime(opening_date, "%Y-%m-%d %H:%M:%S")
                try:
                    first_depreciation_date_import = xlrd.xldate.xldate_as_datetime(first_depreciation_date_import,
                                                                                    book.datemode)
                except TypeError:
                    first_depreciation_date_import = datetime.strptime(first_depreciation_date_import,
                                                                       "%Y-%m-%d %H:%M:%S")
                depreciation_number_import = int(
                    float(depreciation_number_import))
                vals = {
                    "name": asset_name,
                    "asset_sequence": asset_sequence,
                    "responsible_person_id": responsible_employee.id,
                    "asset_location_id": location.id,
                    "model_id": asset_model.id,
                    "acquisition_date": acq_date,
                    "first_depreciation_date": dep_date,
                    "original_value": round(original_value, 2),
                    "already_depreciated_amount_import": round(already_depreciated_amount, 2),
                    "method_number": method_number,
                    "method_period": method_period,
                    "state": state or "draft",
                    "journal_id": journal.id,
                    "account_asset_id": account_asset.id,
                    "account_depreciation_id": account_depreciation.id,
                    "account_depreciation_expense_id": account_expense.id,
                    "opening_date": opening_date,  # Added
                    "depreciation_number_import": depreciation_number_import,  # Added
                    "first_depreciation_date_import": first_depreciation_date_import,  # Added
                    "asset_type": "purchase",
                }
                asset = asset_model_obj.create(vals)
                asset_list.append(asset.id)
                self.action_create_opening_move(asset=asset)
                asset.compute_depreciation_board()
            except Exception as e:
                raise ValidationError(_(f"Row {row_idx + 1}: {str(e)}"))
        return {
            "type": "ir.actions.act_window",
            "name": "Imported Assets",
            "res_model": "account.asset",
            "view_mode": "tree,form",
            "domain": [("id", "in", asset_list)],
        }

    def action_create_opening_move(self, asset):
        if asset:
            line_ids = []
            debit_line = self.prepare_opening_debit_line(asset=asset)
            credit_line = self.prepare_opening_credit_line(asset=asset)
            line_ids.append((0, 0, debit_line))
            line_ids.append((0, 0, credit_line))
            move_vals = {
                'ref': _('Opening Depreciation Move for %s', asset.name),
                'date': asset.opening_date,
                'line_ids': line_ids,
                'journal_id': asset.journal_id.id,
                'is_opening_move_import': True,
                'move_type': 'entry',
                'company_id': asset.company_id.id
            }
            move = self.env["account.move"].create(move_vals)
            move.action_post()
            asset.opening_move_id = move.id

    def prepare_opening_debit_line(self, asset):
        """ return tax debit line """
        return {
            'account_id': asset.account_depreciation_id.id,
            'debit': asset.already_depreciated_amount_import,
            'is_opening_move_import': True,
            'name': _('Opening Depreciation Move for %s', asset.name),
        }

    def prepare_opening_credit_line(self, asset):
        """ return tax credit line """
        return {
            'account_id': asset.account_depreciation_expense_id.id,
            'credit': asset.already_depreciated_amount_import,
            'is_opening_move_import': True,
            'name': _('Opening Depreciation Move for %s', asset.name),
        }

