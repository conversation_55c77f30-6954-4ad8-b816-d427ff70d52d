# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import _, api, fields, models, Command
from odoo.exceptions import UserError, ValidationError


class StockPicking(models.Model):
    _inherit = "stock.picking"

    def _action_done(self):
        res = super()._action_done()
        if self.move_ids_without_package:
            for move in self.move_ids_without_package:
                if move.purchase_line_id:
                    if round(move.purchase_line_id.qty_received, 2) > round(move.purchase_line_id.product_qty, 2):
                        raise UserError(_("PO received qty can't be greater than actual qty"))
        return res