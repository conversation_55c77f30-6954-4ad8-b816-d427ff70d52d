# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import api, fields, models, _


class SaleProfitDb(models.Model):
    _name = "signjet.sale.profit.db.view"
    _description = "Sale Profit Report Db View"
    _auto = False
    _order = 'date desc'

    date = fields.Datetime()
    so_id = fields.Many2one('sale.order', string="SO")
    customer_id = fields.Many2one('res.partner', string="Customer")
    so_amount = fields.Float(string="SO Amount")
    discount = fields.Float(string="Discount")
    amount_tax = fields.Float(string="Tax Amount")
    net_sales = fields.Float(string="Net Sales")
    invoiced_amount = fields.Float(string="Invoice Amount")
    invoiced_discount = fields.Float(string="Invoice Discount")
    net_invoice_amount = fields.Float(string="SO Invoiced")  # 0
    production_material_expense = fields.Float(string="Production Expense")
    workshop_material_expense = fields.Float(string="Workshop Expense")
    sticker_material_expense = fields.Float(string="Sticker Expense")
    material_wastage_expense = fields.Float(string="Material Wastage Expense")
    commission_expense = fields.Float(string="Commission Expense")
    other_expense = fields.Float(string="Other Expense")
    cogs = fields.Float(string="COGS")
    gross_profit = fields.Float(string="Gross Profit")
    salesperson_id = fields.Many2one('res.users', string="Salesperson")
    so_status = fields.Selection(
        [
            ("lead", "Lead"),
            ("draft", "Quotation"),
            ("qualified_lead", "Qualified lead"),
            ("waiting_minimum_sale_price_approval", "Waiting For Minimum Sale Price Approval"),
            ("waiting_gift_sample_approval", "Waiting For Gift and Sample Approval"),
            ("waiting_for_quotation_approval", "Waiting For Quotation Approval"),
            ("quotation_approved", "Quotation Approved"),
            ("payment_request", "Payment Request"),
            ("sent", "Quotation Sent"),
            ("sale", "In Progress"),
            ("completed", "Completed"),
            ("done", "Closed"),
            ("loss", "Loss"),
            ("cancel", "Cancelled"),
        ], string="SO Status"
    )
    so_closed_date = fields.Datetime(string="SO Closed Date")
    company_id = fields.Many2one("res.company", string="Company")

    # ------------------------------------------- Where Query -------------------------------------------
    def _where(self, from_date, to_date, so_id, customer_id):
        """
        Filtering Data
        in starting do not add any space or any character in query
        after end or between you can add
        """
        query = ''
        conditions = []

        if from_date and to_date:
            end_dt = str(to_date) + ' 23:59:59'
            conditions.append(" dt.date >= '{}' AND dt.date <= '{}'".format(from_date, end_dt))
        if so_id:
            conditions.append(" dt.sale_id = {}".format(so_id.id))
        if customer_id:
            conditions.append(" dt.customer = {}".format(customer_id.id))

        if conditions:
            query += " AND " + " AND ".join(conditions)

        query += '''
                AND (
                    dt.so_status IN ('sale', 'completed', 'done')
                )
            '''
        return query.strip()

    def _with(self):
        """ With Query"""
        query = '''
                with invoice as (
                    select 
                        am.sale_id AS sale_id,
                        so.partner_id AS customer,
                        so.company_id AS company_id,
                        so.date_order AS date,
                        so.so_closed_date AS so_closed_date,
                        0.0::numeric AS so_amt,
                        0.0::numeric AS so_discount,
                        0.0::numeric AS net_so_amt,
                        SUM(
                            CASE
                                WHEN so.printing_sale = TRUE THEN COALESCE(am.total_invoice_amount_without_discounts, 0)
                                ELSE COALESCE(am.amount_total, 0)
                            END
                        )::numeric AS invoiced_amount,
                        SUM(
                            CASE
                                WHEN so.printing_sale = TRUE THEN COALESCE(am.total_invoice_discounts, 0)
                                ELSE COALESCE(am.amount_discount, 0)
                            END
                        )::numeric AS inv_discount,
                        SUM(
                            CASE
                                WHEN so.printing_sale = TRUE THEN 
                                    COALESCE(am.total_invoice_amount_without_discounts, 0) 
                                    - COALESCE(am.total_invoice_discounts, 0)
                                ELSE 
                                    COALESCE(am.amount_untaxed, 0) 
                                    - COALESCE(am.amount_discount, 0)
                            END
                        )::numeric AS net_invoice_amount,
                        0.0::numeric AS amount_tax,
                        so.user_id AS sales_person,
                        CASE
                            WHEN so.printing_sale = TRUE THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0::numeric AS production_expense,
                        0.0::numeric AS workshop_expense,
                        0.0 as sticker_expense,
                        0.0::numeric AS comm_exp,
                        0.0::numeric AS other_exp,
                        0.0::numeric AS cogs_exp,
                        0.0::numeric AS gross_profit,
                        0.0::numeric AS hr_other_expense,
                        0.0::numeric AS material_wastage_expense
                    FROM account_move am
                    JOIN sale_order so ON so.id = am.sale_id
                    WHERE am.state = 'posted' 
                      AND am.move_type IN ('out_invoice', 'out_refund')
                    GROUP BY 
                        am.sale_id, 
                        so.partner_id, 
                        so.company_id, 
                        so.date_order, 
                        so.so_closed_date, 
                        so.user_id, 
                        so.printing_sale_status, 
                        so.printing_sale, 
                        so.state
                ),
                sale_amount as (
                    select 
                        so.id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        (CASE
                            WHEN so.printing_sale THEN so.total_ps_amount_without_discounts
                            ELSE so.amount_total
                        END)::numeric AS so_amt,
                        (CASE
                            WHEN so.printing_sale THEN so.total_ps_discounts
                            ELSE so.amount_discount
                        END)::numeric AS so_discount,
                        (CASE
                            WHEN so.printing_sale THEN (so.total_ps_amount_without_discounts - so.total_ps_discounts)
                            ELSE (so.amount_untaxed - so.amount_discount)
                        END)::numeric AS net_so_amt,
                        0.0 as invoiced_amount,
                        0.0::numeric as inv_discount,
                        0.0::numeric as net_invoice_amount,
                        so.amount_tax::numeric as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0::numeric as production_expense,
                        0.0::numeric as workshop_expense,
                        0.0 as sticker_expense,
                        0.0::numeric as comm_exp,
                        0.0::numeric as other_exp,
                        0.0::numeric as cogs_exp,
                        0.0::numeric as gross_profit,
                        0.0::numeric as hr_other_expense,
                        0.0::numeric as material_wastage_expense
                    from sale_order so
                    group by 
                        so.id, 
                        so.company_id, 
                        so.user_id, 
                        so.printing_sale_status, 
                        so.partner_id, 
                        so.date_order, 
                        so.so_closed_date, 
                        so.printing_sale, 
                        so.state
                ),
                product_exp as (
                    select 
                        smc.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        sum(
                            CASE
                                WHEN smc.source_location_id = sp.location_id AND smc.consume_location_id = sp.location_dest_id THEN am.amount_total_signed
                                WHEN smc.source_location_id = sp.location_dest_id AND smc.consume_location_id = sp.location_id THEN -am.amount_total_signed
                                ELSE 0
                            END
                        ) as production_expense,
                        0.0 as workshop_expense,
                        0.0 as sticker_expense,
                        0.0 as comm_exp,
                        0.0 as other_exp,
                        0.0 as cogs_exp,
                        0.0 as gross_profit,
                        0.0 as hr_other_expense,
                        0.0 as material_wastage_expense
                    from supply_material_consume smc
                    join stock_picking sp on sp.supply_material_consume_id=smc.id
                    join stock_move sm on sm.picking_id=sp.id
                    join account_move am on am.stock_move_id=sm.id
                    join sale_order so on so.id=smc.sale_id
                    join sp_task_process stp on stp.id=smc.task_id
                    where stp.order_task_type='production'
                    group by smc.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id, 
                        so.date_order, so.so_closed_date, so.printing_sale, so.state
                ),
                workshop_exp as (
                    select 
                        smc.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0 as production_expense,
                        sum(
                            CASE
                                WHEN smc.source_location_id = sp.location_id AND smc.consume_location_id = sp.location_dest_id THEN am.amount_total_signed
                                WHEN smc.source_location_id = sp.location_dest_id AND smc.consume_location_id = sp.location_id THEN -am.amount_total_signed
                                ELSE 0
                            END
                        ) as workshop_expense,
                        0.0 as sticker_expense,
                        0.0 as comm_exp,
                        0.0 as other_exp,
                        0.0 as cogs_exp,
                        0.0 as gross_profit,
                        0.0 as hr_other_expense,
                        0.0 as material_wastage_expense
                    from supply_material_consume smc
                    join stock_picking sp on sp.supply_material_consume_id=smc.id
                    join stock_move sm on sm.picking_id=sp.id
                    join account_move am on am.stock_move_id=sm.id
                    join sale_order so on so.id=smc.sale_id
                    join sp_task_process stp on stp.id=smc.task_id
                    where stp.order_task_type='delivery'
                        and stp.delivery_type='workshop'
                    group by smc.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id, 
                        so.date_order, so.so_closed_date, so.printing_sale, so.state
                ),
                sticker_exp as (
                    select 
                        smc.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0 as production_expense,
                        0.0 as workshop_expense,
                        sum(
                            CASE
                                WHEN smc.source_location_id = sp.location_id AND smc.consume_location_id = sp.location_dest_id THEN am.amount_total_signed
                                WHEN smc.source_location_id = sp.location_dest_id AND smc.consume_location_id = sp.location_id THEN -am.amount_total_signed
                                ELSE 0
                            END
                        ) as sticker_expense,
                        0.0 as comm_exp,
                        0.0 as other_exp,
                        0.0 as cogs_exp,
                        0.0 as gross_profit,
                        0.0 as hr_other_expense,
                        0.0 as material_wastage_expense
                    from supply_material_consume smc
                    join stock_picking sp on sp.supply_material_consume_id=smc.id
                    join stock_move sm on sm.picking_id=sp.id
                    join account_move am on am.stock_move_id=sm.id
                    join sale_order so on so.id=smc.sale_id
                    join sp_task_process stp on stp.id=smc.task_id
                    where stp.order_task_type='delivery'
                        and stp.delivery_type='sticker'
                    group by smc.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id, 
                        so.date_order, so.so_closed_date, so.printing_sale, so.state
                ),
                material_wastage_exp as (
                    select 
                        stp.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0 as production_expense,
                        0.0 as workshop_expense,
                        0.0 as sticker_expense,
                        0.0 as comm_exp,
                        0.0 as other_exp,
                        0.0 as cogs_exp,
                        0.0 as gross_profit,
                        0.0 as hr_other_expense,
                        sum(COALESCE(aml.balance, 0)) as material_wastage_expense
                    from account_move_line aml
                    join account_move am ON am.id = aml.move_id
                    join stock_move sm ON sm.id = am.stock_move_id
                    join sp_task_process stp ON stp.id = sm.task_id
                    join sale_order so ON so.id = stp.sale_id
                    join stock_location sl ON sl.id = sm.location_dest_id
                    where sl.usage = 'inventory'
                        and sl.scrap_location = true
                        and sl.company_id = so.company_id
                        and am.state = 'posted'
                        and (aml.account_id = sl.current_asset_account_id
                            or aml.account_id = sl.valuation_in_account_id)
                    group by stp.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id, 
                        so.date_order, so.so_closed_date, so.printing_sale, so.state
                ),
                commission as (
                    select 
                        ce.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0 as production_expense,
                        0.0 as workshop_expense,
                        0.0 as sticker_expense,
                        sum(ce.commission_amount) as comm_exp,
                        0.0 as other_exp,
                        0.0 as cogs_exp,
                        0.0 as gross_profit,
                        0.0 as hr_other_expense,
                        0.0 as material_wastage_expense
                    from res_commission_entry ce
                    join sale_order so on so.id=ce.sale_id
                    group by ce.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id, 
                        so.date_order, so.so_closed_date, so.printing_sale, so.state
                ),
                other_expenses as (
                    select
                        aml.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0 as production_expense,
                        0.0 as workshop_expense,
                        0.0 as sticker_expense,
                        0.0 as comm_exp,
                        ABS(SUM(COALESCE(ABS(aml.balance), 0))) AS other_exp,
                        0.0 as cogs_exp,
                        0.0 as gross_profit,
                        0.0 as hr_other_expense,
                        0.0 as material_wastage_expense
                    from account_move_line aml
                    join sale_order so ON so.id = aml.sale_id
                    join account_move am ON am.id = aml.move_id
                    join account_account aa ON aa.id = aml.account_id
                    join account_account_type aat ON aa.user_type_id = aat.id
                    where am.state = 'posted'
                    and aat.internal_group = 'expense'
                    group by aml.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id,
                             so.date_order, so.so_closed_date, so.printing_sale, so.state
                ),
                other_hr_expenses as (
                    select
                        exp.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0 as production_expense,
                        0.0 as workshop_expense,
                        0.0 as sticker_expense,
                        0.0 as comm_exp,
                        0.0 as other_exp,
                        0.0 as cogs_exp,
                        0.0 as gross_profit,
                        sum(coalesce(exp.unit_amount, 0)) / 2 as hr_other_expense,
                        0.0 as material_wastage_expense
                    from hr_expense exp
                    join sale_order so on so.id = exp.sale_id
                    where exp.state in ('done', 'post')
                    group by exp.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id,
                    so.date_order, so.so_closed_date, so.printing_sale, so.state
                    ),
                cogs_expense as (
                    select 
                        sp.sale_id as sale_id,
                        so.partner_id as customer,
                        so.company_id as company_id,
                        so.date_order as date,
                        so.so_closed_date AS so_closed_date,
                        0.0 as so_amt,
                        0.0 as so_discount,
                        0.0 as net_so_amt,
                        0.0 as invoiced_amount,
                        0.0 as inv_discount,
                        0.0 as net_invoice_amount,
                        0.0 as amount_tax,
                        so.user_id as sales_person,
                        CASE
                            WHEN so.printing_sale = true THEN so.printing_sale_status
                            ELSE so.state
                        END AS so_status,
                        0.0 as production_expense,
                        0.0 as workshop_expense,
                        0.0 as sticker_expense,
                        0.0 as comm_exp,
                        0.0 as other_exp,
                        SUM(svl.value) AS cogs_exp,
                        0.0 as gross_profit,
                        0.0 as hr_other_expense,
                        0.0 as material_wastage_expense
                    from stock_valuation_layer svl
                    join stock_move sm on sm.id = svl.stock_move_id
                    join stock_picking sp on sp.id = sm.picking_id
                    join sale_order so on so.id = sp.sale_id
                    join sale_order_line sol ON sol.order_id = so.id AND sol.product_id = sm.product_id
                    left join stock_picking sp_check on sp.id = sp_check.id
                    where sp_check.task_id IS NULL
                    group by sp.sale_id, so.company_id, so.user_id, so.printing_sale_status, so.partner_id, 
                        so.date_order, so.so_closed_date, so.printing_sale, so.state
                ),   
                data as (
                    select * from invoice
                    union
                    select * from sale_amount
                    union
                    select * from product_exp
                    union
                    select * from workshop_exp
                    union
                    select * from sticker_exp
                    union
                    select * from material_wastage_exp
                    union
                    select * from commission
                    union
                    select * from other_expenses
                    union
                    select * from other_hr_expenses
                    union
                    select * from cogs_expense
                )
        '''
        return query

    def get_report(self, from_date, to_date, sale_id, customer_id):
        """ Create Database View """
        where_query = self._where(from_date, to_date, sale_id, customer_id)
        with_query = self._with()
        view_query = '''CREATE OR REPLACE VIEW signjet_sale_profit_db_view AS( %s %s)'''
        select_query = '''
            SELECT 
                row_number() OVER() as id,
                dt.sale_id as so_id, 
                dt.customer as customer_id,
                dt.company_id as company_id,
                dt.date as date,
                dt.so_closed_date as so_closed_date,
                sum(dt.so_amt) as so_amount,
                sum(dt.so_discount) as discount,
                sum(dt.net_so_amt) as net_sales,
                sum(dt.invoiced_amount) as invoiced_amount,
                sum(dt.inv_discount) as invoiced_discount,
                sum(dt.net_invoice_amount) as net_invoice_amount,
                sum(dt.amount_tax) as amount_tax,
                dt.sales_person as salesperson_id,
                dt.so_status as so_status,
                sum(dt.production_expense) as production_material_expense,
                sum(dt.workshop_expense) as workshop_material_expense,
                sum(dt.sticker_expense) as sticker_material_expense,
                sum(dt.material_wastage_expense) as material_wastage_expense,
                sum(dt.comm_exp) as commission_expense,
                CASE
                    WHEN SUM(COALESCE(dt.other_exp, 0)) != 0.0 THEN
                        SUM(COALESCE(dt.other_exp, 0)) 
                        + SUM(COALESCE(dt.hr_other_expense, 0)) 
                        - SUM(COALESCE(dt.production_expense, 0)) 
                        - SUM(COALESCE(dt.workshop_expense, 0)) 
                        - SUM(COALESCE(dt.sticker_expense, 0)) 
                        - SUM(COALESCE(dt.material_wastage_expense, 0))
                    ELSE
                        SUM(COALESCE(dt.other_exp, 0)) + SUM(COALESCE(dt.hr_other_expense, 0))
                END AS other_expense,
                -(sum(dt.cogs_exp)) as cogs,
                sum(dt.net_invoice_amount) - (
                    sum(COALESCE(dt.production_expense, 0)) + 
                    sum(COALESCE(dt.workshop_expense, 0)) + 
                    sum(COALESCE(dt.sticker_expense, 0)) + 
                    sum(COALESCE(dt.material_wastage_expense, 0)) + 
                    sum(COALESCE(dt.comm_exp, 0)) + 
                    sum(COALESCE(dt.other_exp, 0)))
                ) as gross_profit
            from data dt
        '''
        if where_query:
            # to remove AND
            where_query = where_query[4:]
            select_query += 'where'
            select_query += where_query
            select_query += '''group by dt.sale_id, dt.customer, dt.company_id, dt.sales_person, dt.so_status, 
            dt.date, dt.so_closed_date'''
            query = view_query % (with_query, select_query)
            self._cr.execute(query)
        else:
            select_query += '''group by dt.sale_id, dt.customer, dt.company_id, dt.sales_person, dt.so_status, 
            dt.date, dt.so_closed_date'''
            query = view_query % (with_query, select_query)
            self._cr.execute(query)

    def action_open_sale_accounting_entry(self):
        """Open move line"""
        return {
            'name': _('Journal Items'),
            'res_model': 'account.move.line',
            'view_mode': 'tree,form',
            'domain': [
                '|', '|', '|',
                ('move_id.sale_id', '=', self.so_id.id),
                ('sale_id', '=', self.so_id.id),
                ('move_id.stock_move_id.picking_id.sale_id', '=', self.so_id.id),
                ('task_id.sale_id', '=', self.so_id.id),
                ('account_id.user_type_id.internal_group', 'in', ['income', 'expense']),
                ('move_id.state', '=', 'posted')
            ],
            'target': 'current',
            'type': 'ir.actions.act_window',
        }

    def action_view_sale_commission_entry(self):
        """Open move line"""
        return {
            'name': _('Commission Entry'),
            'res_model': 'res.commission.entry',
            'view_mode': 'tree,form',
            'domain': [
                ('sale_id', '=', self.so_id.id),
            ],
            'target': 'current',
            'type': 'ir.actions.act_window',
        }
