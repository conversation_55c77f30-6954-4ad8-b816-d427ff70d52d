# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import _, api, fields, models, Command
from odoo.exceptions import UserError
from datetime import datetime


class SaleOvertime(models.Model):
    _name = "sale.overtime"
    _inherit = "mail.thread"
    _rec_name = "name"
    _order = "id desc,create_date desc"

    name = fields.Char(string="Name")
    sale_id = fields.Many2one("sale.order", string="Sale Order #")
    sale_line_id = fields.Many2one("sale.order.line", string="Sale Line")
    user_id = fields.Many2one("res.users", string="Overtime Employee")
    partner_id = fields.Many2one("res.partner", string="Customer")
    company_id = fields.Many2one("res.company", string="Company")
    product_id = fields.Many2one("product.product", string="Product")
    overtime_scope = fields.Selection(
        [
            ("sales", "Sales"),
            ("design", "Design"),
            ("production", "Production"),
            ("workshop", "Workshop"),
            ("sticker", "Sticker"),
            ("billboard", "Billboard"),
            ("screen_printing", "Screen Printing"),
        ]
    )
    charged_to_customer = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string="Charged to Customer",
        default="no"
    )
    so_amount_total = fields.Monetary(
        string="SO Amount Total",
        related="sale_id.amount_total",
        store=True
    )
    currency_id = fields.Many2one(
        "res.currency",
        string="Currency",
        default=lambda self: self.env.company.currency_id
    )
    sale_overtime_line_ids = fields.One2many("sale.overtime.line", "sale_overtime_id")
    reason_remark = fields.Char(string="Reason For Overtime")
    reason_for_approval = fields.Char(string="Reason For Approval")
    qty = fields.Float(string="Quantity")
    price_unit = fields.Float(string="Unit Price")
    price_subtotal = fields.Float(string="Subtotal")
    overtime_approved = fields.Boolean(string="Overtime Approved", default=False)
    overtime_state = fields.Selection(
        [
            ("draft", "Draft"),
            ("approved", "Approved"),
            ("rejected", "Rejected"),
            ("posted", "Posted"),
            ("paid", "Paid"),
        ],
        string="Overtime State",
        default="draft",
    )
    overtime_payment = fields.Selection(
        [
            ("paid_with_payroll", "Pay With Payroll"),
            ("paid_separately", "Pay Separately"),
        ],
        string="Overtime Payment",
    )
    overtime_move_ids = fields.Many2many(
        "account.move",
        "overtime_account_move_rel",
        string="Overtime Move"
    )
    date = fields.Date(string="Date")
    start_time = fields.Float(string="Start Time")
    end_time = fields.Float(string="End Time")
    overtime_duration = fields.Float(string="Overtime Duration")
    final_amount = fields.Float(string="Final Amount")
    overtime_rate_per_hour = fields.Float(string="Overtime Rate Per Hour")
    overtime_income = fields.Float(string="Overtime Income")
    payment_ids = fields.Many2many(
        "account.payment",
        "overtime_account_payment_rel",
        string="Overtime Payment"
    )

    @api.model
    def create(self, vals):
        """
        Generate sequence
        """
        if vals.get("name", _("New")) == _("New"):
            vals["name"] = self.env["ir.sequence"].next_by_code(
                "sale.overtime"
            ) or _("New")
        return super().create(vals)

    @api.onchange("overtime_duration", "overtime_rate_per_hour")
    def onchange_overtime_duration(self):
        if self.overtime_duration:
            self.final_amount = self.overtime_duration * self.overtime_rate_per_hour

    @api.onchange("start_time", "end_time")
    def onchange_start_and_end_date(self):
        if self.start_time and self.end_time:
            self.overtime_duration = self.end_time - self.start_time

    def action_approve_overtime(self):
        if any(line.overtime_duration <= 0.0 for line in self.sale_overtime_line_ids):
            raise UserError(_("Please provide correct start and end time in each line"))
        self.overtime_approved = True
        self.overtime_state = "approved"

    @api.onchange("sale_overtime_line_ids")
    def onchange_overtime_line_get_rate(self):
        for line in self.sale_overtime_line_ids:
            line.overtime_rate_per_hour = self.company_id.overtime_rate_per_hour

    @api.constrains("sale_overtime_line_ids")
    def onchange_overtime_line_rate(self):
        for line in self.sale_overtime_line_ids:
            line.overtime_rate_per_hour = self.company_id.overtime_rate_per_hour

    def action_reject_overtime(self):
        if self.overtime_scope == "sales" and self.env.user.has_group("sale_process.printing_sale_user"):
            self.overtime_approved = False
            self.overtime_state = "rejected"
        elif self.overtime_scope == "design" and self.env.user.has_group(
                "common_task_process.common_task_process_design_user"):
            self.overtime_approved = False
            self.overtime_state = "rejected"
        elif self.overtime_scope == "production" and self.env.user.has_group(
                "common_task_process.common_task_process_production_user"):
            self.overtime_approved = False
            self.overtime_state = "rejected"
        elif self.overtime_scope == "workshop" and self.env.user.has_group(
                "common_task_process.common_task_process_workshop_process_user"):
            self.overtime_approved = False
            self.overtime_state = "rejected"
        elif self.overtime_scope == "sticker" and self.env.user.has_group(
                "common_task_process.common_task_process_sticker_process_user"):
            self.overtime_approved = False
            self.overtime_state = "rejected"
        else:
            raise UserError(_("You don't have permission to reject this Overtime"))

    def action_reset_to_draft(self):
        if self.overtime_scope == "sales" and self.env.user.has_group("sale_process.printing_sale_user"):
            self.overtime_approved = False
            self.overtime_state = "draft"
        elif self.overtime_scope == "design" and self.env.user.has_group(
                "common_task_process.common_task_process_design_user"):
            self.overtime_approved = False
            self.overtime_state = "draft"
        elif self.overtime_scope == "production" and self.env.user.has_group(
                "common_task_process.common_task_process_production_user"):
            self.overtime_approved = False
            self.overtime_state = "draft"
        elif self.overtime_scope == "workshop" and self.env.user.has_group(
                "common_task_process.common_task_process_workshop_process_user"):
            self.overtime_approved = False
            self.overtime_state = "draft"
        elif self.overtime_scope == "sticker" and self.env.user.has_group(
                "common_task_process.common_task_process_sticker_process_user"):
            self.overtime_approved = False
            self.overtime_state = "draft"
        else:
            raise UserError(_("You don't have permission to reset this Overtime"))

    def create_overtime_debit_move_line_vals(self, partner_id, amount, account_id):
        """
        Debit Line Dictionary for the account move line
        :param partner_id:
        :param amount:
        :return:
        """
        debit_vals = {
            'name': 'Sale Overtime Entry ' + partner_id.name,
            'debit': amount,
            'credit': 0.0,
            'partner_id': partner_id.id,
            'sale_id': self.sale_id.id,
            'account_id': account_id.id,
            'branch_id': self.sale_id.branch_id.id if self.sale_id.branch_id else False,
            'company_id': self.company_id.id if self.company_id else False,
            'date': datetime.now().date()
        }
        return debit_vals

    def create_overtime_credit_move_line_vals(self, partner_id, amount, account_id):
        credit_vals = {
            'name': 'Sale Overtime Entry ' + partner_id.name,
            'debit': 0.0,
            'credit': amount,
            'partner_id': partner_id.id,
            'sale_id': self.sale_id.id,
            'account_id': account_id.id,
            'branch_id': self.sale_id.branch_id.id if self.sale_id.branch_id else False,
            'company_id': self.company_id.id if self.company_id else False,
            'date': fields.Date.context_today(self)
        }
        return credit_vals

    def prepare_overtime_move_entry_line_values(self, line):
        debit_account_id = self.company_id.overtime_expense_account_id
        credit_account_id = line.employee_id.address_home_id.property_account_payable_id
        if not debit_account_id or not credit_account_id:
            raise UserError(_("Please provide correct debit and credit account"))
        amount = abs(line.final_amount)
        debit_line = self.create_overtime_debit_move_line_vals(
            partner_id=line.employee_id.address_home_id,
            amount=amount,
            account_id=debit_account_id
        )
        credit_line = self.create_overtime_credit_move_line_vals(
            partner_id=line.employee_id.address_home_id,
            amount=amount,
            account_id=credit_account_id
        )
        return [(0, 0, debit_line), (0, 0, credit_line)]

    def prepare_overtime_move_entry_values(self, line):
        if not self.company_id.overtime_move_journal_id:
            raise UserError(_("Please configure the overtime journal to post the overtime"))
        entry_values = {
            'partner_id': line.employee_id.address_home_id.id,
            'overtime_employee_id': line.employee_id.id,
            'state': 'draft',
            'move_type': 'entry',
            'journal_id': self.company_id.overtime_move_journal_id.id,
            'date': fields.Date.context_today(self),
            'branch_id': self.sale_id.branch_id.id if self.sale_id.branch_id else False,
            'company_id': self.company_id.id if self.company_id else False,
            'overtime_move': True,
            'overtime_id': self.id,
            'sale_id': self.sale_id.id,
            'ref': self.name + " Sale Overtime Move "
        }
        return entry_values

    def create_overtime_accounting_entry(self):
        for line in self.sale_overtime_line_ids:
            overtime_move_entry_date = self.prepare_overtime_move_entry_values(line=line)
            overtime_move_entry_date['line_ids'] = self.prepare_overtime_move_entry_line_values(line=line)
            overtime_entry_move_id = self.env["account.move"].create(overtime_move_entry_date)
            line.overtime_line_move_ids |= overtime_entry_move_id
            self.overtime_move_ids |= overtime_entry_move_id
            overtime_entry_move_id.post()
            line.overtime_state = "posted"
        self.overtime_state = "posted"

    def action_post_overtime(self):
        if self.overtime_approved:
            if self.overtime_payment == "paid_separately":
                self.create_overtime_accounting_entry()
                self.overtime_state = "posted"

    def action_view_overtime_move(self):
        action = self.env.ref('account.action_move_out_invoice_type').read()[0]
        action['domain'] = [('id', 'in', self.overtime_move_ids.ids)]
        return action

    def action_register_overtime_payment(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Register Overtime Payment',
            'res_model': 'sale.overtime.payment.wiz',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_overtime_id': self.id,
                'default_company_id': self.company_id.id,
                'default_journal_id': self.company_id.overtime_payment_journal_id.id,
            },
        }

    def action_register_multiple_overtime_payment(self, records):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Register Overtime Payment',
            'res_model': 'sale.overtime.payment.wiz',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_overtime_id': self.id,
                'default_overtime_rec_ids': records.ids,
                'default_company_id': self.company_id.id,
                'default_journal_id': self.company_id.overtime_payment_journal_id.id,
            },
        }

    def action_view_overtime_payments(self):
        action = self.env.ref('account.action_account_payments_payable').read()[0]
        action['domain'] = [('id', 'in', self.payment_ids.ids)]
        return action


class SaleOvertimeLine(models.Model):
    _name = "sale.overtime.line"

    sale_overtime_id = fields.Many2one("sale.overtime", string="Sale Overtime")
    sale_id = fields.Many2one("sale.order", string="Sale Order")
    sale_line_id = fields.Many2one("sale.order.line", string="Sale Line")
    overtime_scope = fields.Selection(
        [
            ("sales", "Sales"),
            ("design", "Design"),
            ("production", "Production"),
            ("workshop", "Workshop"),
            ("sticker", "Sticker"),
            ("billboard", "Billboard"),
            ("screen_printing", "Screen Printing"),
        ]
    )
    overtime_state = fields.Selection(
        [
            ("draft", "Draft"),
            ("approved", "Approved"),
            ("rejected", "Rejected"),
            ("posted", "Posted"),
        ],
        string="Overtime State",
        default="draft",
    )
    department_ids = fields.Many2many(
        "hr.department",
        "overtime_scope_department_rels",
        string="Department"
    )
    user_ids = fields.Many2many("res.users", string="Overtime Employee")
    user_id = fields.Many2one("res.users", string="Overtime Employee")
    employee_ids = fields.Many2many(
        "hr.employee",
        "overtime_employee_rel",
        string="Overtime Employees",
        compute="compute_overtime_employees",
        store=True
    )
    employee_id = fields.Many2one("hr.employee", string="Overtime Employee")
    overtime_rate_per_hour = fields.Float(string="Overtime Rate Per Hour")
    start_time = fields.Float(string="Start Time")
    end_time = fields.Float(string="End Time")
    start_date_time = fields.Datetime(string="Start Time")
    end_date_time = fields.Datetime(string="End Time")
    final_duration = fields.Float(string="Final Duration", compute="compute_start_duration_and_end_duration")
    overtime_duration_time = fields.Float(string="Overtime Duration")
    overtime_duration = fields.Float(string="Overtime Duration")
    final_amount = fields.Float(string="Overtime Amount")
    reason_remark = fields.Char(string="Reason Remark")
    is_line_value_updated = fields.Boolean(string="Is Line Value Updated")
    overtime_line_move_ids = fields.Many2many(
        "account.move",
        "overtime_line_account_move_rel",
        string="Overtime Move"
    )

    @api.onchange("overtime_duration", "overtime_rate_per_hour")
    def onchange_overtime_duration(self):
        if self.overtime_duration:
            self.final_amount = self.overtime_duration * self.overtime_rate_per_hour

    @api.onchange("start_time", "end_time", "overtime_rate_per_hour")
    def onchange_start_and_end_date(self):
        if self.start_time and self.end_time:
            self.overtime_duration = self.end_time - self.start_time
            self.overtime_duration_time = self.end_time - self.start_time

    @api.depends("start_date_time", "end_date_time", "overtime_rate_per_hour")
    def compute_start_duration_and_end_duration(self):
        for line in self:
            line.final_duration = 0.0
            if line.start_date_time and line.end_date_time:
                if line.start_date_time >= line.end_date_time:
                    raise UserError(_("Start Date Time must be less than End Date Time"))
                duration = line.end_date_time - line.start_date_time
                line.final_duration = duration.total_seconds() / 3600.0
                line.overtime_duration = line.final_duration
                line.overtime_duration_time = line.final_duration
                line.onchange_overtime_duration()

    @api.onchange("user_id")
    def onchange_overtime_user(self):
        self.overtime_rate_per_hour = self.sale_overtime_id.overtime_rate_per_hour

    @api.onchange("overtime_scope")
    def onchange_overtime_rate(self):
        if self.overtime_scope:
            self.department_ids = False
            overtime_scope_master = self.env["overtime.scope.master"].sudo().search(
                [
                    ("overtime_scope", "=", self.overtime_scope),
                ], limit=1
            )
            if overtime_scope_master:
                self.department_ids = overtime_scope_master.department_ids
            self.compute_overtime_employees()

    def compute_overtime_employees(self):
        for line in self:
            line.employee_ids = False
            if line.overtime_scope and line.department_ids:
                employee_ids = self.env["hr.employee"].sudo().search(
                    [
                        ("department_id", "in", line.department_ids.ids),
                        ("company_id", "=", self.env.company.id),
                    ]
                )
                line.employee_ids = employee_ids

    @api.onchange("overtime_scope")
    def onchange_overtime_scope_get_users(self):
        if self.overtime_scope:
            self.user_ids = False
            user_ids = self.env["res.users"].sudo().search(
                [
                    ("active", "=", True),
                    ("company_ids", "in", self.sale_overtime_id.company_id.id)
                ]
            )
            if user_ids:
                for user in user_ids:
                    if (user.has_group('sale_process.printing_sale_user') and
                            self.overtime_scope == "sales"):
                        self.user_ids |= user
                    if (user.has_group('common_task_process.common_task_process_design_user') and
                            self.overtime_scope == "design"):
                        self.user_ids |= user
                    if (user.has_group('common_task_process.common_task_process_production_user') and
                            self.overtime_scope == "production"):
                        self.user_ids |= user
                    if (user.has_group('common_task_process.common_task_process_workshop_process_user') and
                            self.overtime_scope == "workshop"):
                        self.user_ids |= user
                    if (user.has_group('common_task_process.common_task_process_sticker_process_user') and
                            self.overtime_scope == "sticker"):
                        self.user_ids |= user

    @api.onchange("user_id", "start_time", "end_time",
                    "overtime_duration_time", "final_amount", "reason_remark")
    def check_overtime_duration(self):
        if self.user_id:
            self.is_line_value_updated = True

    @api.constrains("is_line_value_updated", "user_id", "start_time", "end_time",
                    "overtime_duration_time", "final_amount", "reason_remark")
    def check_for_department_manager(self):
        for line in self:
            if line.is_line_value_updated:
                if line.overtime_scope == "design" and not self.env.user.has_group("common_task_process.common_task_process_design_manager"):
                    raise UserError(_(f"You don't have permission to update {line.overtime_scope} overtime line"))
                elif line.overtime_scope == "production" and not self.env.user.has_group("common_task_process.common_task_process_production_manager"):
                    raise UserError(_(f"You don't have permission to update {line.overtime_scope} overtime line"))
                elif line.overtime_scope == "workshop" and not self.env.user.has_group("common_task_process.common_task_process_workshop_process_manager"):
                    raise UserError(_(f"You don't have permission to update {line.overtime_scope} overtime line"))
                elif line.overtime_scope == "sticker" and not self.env.user.has_group("common_task_process.common_task_process_sticker_process_manager"):
                    raise UserError(_(f"You don't have permission to update {line.overtime_scope} overtime line"))


