<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_sale_overtime_tree" model="ir.ui.view">
        <field name="name">sale.overtime.tree</field>
        <field name="model">sale.overtime</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="sale_id"/>
                <field name="user_id"/>
                <field name="partner_id"/>
                <field name="final_amount"/>
                <field name="overtime_payment"/>
                <field name="overtime_state"/>
            </tree>
        </field>
    </record>

    <record id="view_sale_overtime_form" model="ir.ui.view">
        <field name="name">sale.overtime.form</field>
        <field name="model">sale.overtime</field>
        <field name="arch" type="xml">
            <form string="Sale Overtime">
                <header>
                    <button name="action_approve_overtime" string="Approve" type="object"
                            class="oe_highlight" confirm="Are you sure you want to Approve!!"
                            groups="printing_sale_overtime.overtime_approval_manager"
                            attrs="{'invisible': [('overtime_state', '!=', 'draft')]}"/>
                    <button name="action_reject_overtime" string="Reject" type="object"
                            class="oe_highlight" confirm="Are you sure you want to Reject!!"
                            groups="printing_sale_overtime.overtime_approval_manager"
                            attrs="{'invisible': [('overtime_state', '!=', 'draft')]}"/>
                    <button name="action_reset_to_draft" string="Reset To Draft" type="object"
                            class="oe_highlight" confirm="Are you sure you want to Reset To Draft!!"
                            groups="printing_sale_overtime.overtime_approval_manager"
                            attrs="{'invisible': [('overtime_state', '!=', 'rejected')]}"/>
                    <button name="action_post_overtime" string="Post Overtime" type="object"
                            class="oe_highlight" confirm="Are you sure you want to post overtime!!"
                            groups="printing_sale_overtime.overtime_approval_manager"
                            attrs="{'invisible': ['|', ('overtime_state', '!=', 'approved'), ('overtime_payment', '=', 'paid_with_payroll')]}"/>
                    <button name="action_register_overtime_payment" string="Register Payment" type="object"
                            class="oe_highlight" groups="printing_sale_overtime.overtime_approval_manager"
                            attrs="{'invisible': ['|', ('overtime_state', '!=', 'posted'), ('overtime_payment', '=', 'paid_with_payroll')]}"/>
                    <field name="overtime_state" widget="statusbar" statusbar_visible="draft,approved"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_overtime_move" string="Accounting Entries"
                                type="object" class="oe_stat_button" icon="fa-pencil-square-o"
                                attrs="{'invisible': [('overtime_move_ids', '=', False)]}"/>
                        <button name="action_view_overtime_payments" string="Payment Entries"
                                type="object" class="oe_stat_button" icon="fa-pencil-square-o"
                                attrs="{'invisible': [('payment_ids', '=', False)]}"/>
                    </div>
                    <div class="oe_title">
                        <h1 class="d-flex">
                            <field name="name" placeholder="Overtime" nolabel="1" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="date" readonly="1"/>
                            <field name="sale_id" options="{'no_open': True}" readonly="1"/>
                            <field name="partner_id" options="{'no_open': True}" readonly="1"/>
                            <field name="so_amount_total" force_save="1" readonly="1" widget="monetary"/>
                            <field name="charged_to_customer" readonly="1"/>
                            <field name="product_id" options="{'no_open': True}" readonly="1" invisible="1"/>
                            <field name="overtime_income" readonly="1" force_save="1"
                                   attrs="{'invisible': [('charged_to_customer', '=', 'no')]}"/>
                            <field name="reason_for_approval" required="1"
                                   attrs="{'readonly': [('overtime_state', '!=', 'draft')]}"/>
                            <field name="payment_ids" invisible="1" widget="many2many_tags"/>
                        </group>
                        <group>
                            <field name="qty" readonly="1" invisible="1"/>
                            <field name="price_unit" readonly="1" invisible="1"/>
                            <field name="price_subtotal" readonly="1" invisible="1"/>
                            <field name="overtime_payment" readonly="1"/>
                            <field name="final_amount" readonly="1" force_save="1" invisible="1"/>
                            <field name="company_id" options="{'no_open': True}" readonly="1"/>
                            <field name="sale_line_id" invisible="1" options="{'no_open': True}" readonly="1"/>
                            <field name="overtime_move_ids" invisible="1" widget="many2many_tags" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Overtime Line">
                            <field name="sale_overtime_line_ids" widget="one2many" force_save="1"
                                   attrs="{'readonly': [('overtime_state', '!=', 'draft')]}">
                                <tree editable="bottom">
                                    <field name="sale_overtime_id" invisible="1"/>
                                    <field name="sale_id" options="{'no_open': True}"
                                           force_save="1" readonly="1" invisible="1"/>
                                    <field name="sale_line_id" invisible="1"
                                           force_save="1" options="{'no_open': True}" readonly="1"/>
                                    <field name="overtime_scope" force_save="1"/>
                                    <field name="department_ids" force_save="1" invisible="1"
                                           widget="many2many_tags" readonly="1"/>
                                    <field name="employee_ids" force_save="1" widget="many2many_tags" invisible="1"/>
                                    <field name="employee_id" options="{'no_open': True}" required="1"
                                           force_save="1" domain="[('id', 'in', employee_ids)]"/>
                                    <field name="overtime_rate_per_hour" readonly="1" force_save="1"/>
                                    <!--                                    <field name="start_time" force_save="1" widget="float_time"/>-->
                                    <field name="is_line_value_updated" force_save="1" readonly="1" invisible="1"/>
                                    <!--                                    <field name="end_time" force_save="1" widget="float_time"/>-->

                                    <field name="start_date_time" widget="datetime"/>
                                    <field name="end_date_time" widget="datetime"/>
                                    <field name="final_duration" force_save="1" invisible="1"/>

                                    <field name="overtime_duration" invisible="1" readonly="1" force_save="1"/>
                                    <field name="overtime_duration_time" readonly="1" force_save="1"
                                           widget="float_time"/>
                                    <field name="final_amount" readonly="1" force_save="1"/>
                                    <field name="reason_remark" force_save="1"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="action_printing_sale_overtime" model="ir.actions.act_window">
        <field name="name">Sale Overtime</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.overtime</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': False}</field>
        <field name="target">current</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('printing_sale_overtime.view_sale_overtime_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('printing_sale_overtime.view_sale_overtime_form')})]"/>
    </record>

    <record id="action_register_overtime_multi_payment" model="ir.actions.server">
            <field name="name">Register Overtime Payment</field>
            <field name="model_id" ref="printing_sale_overtime.model_sale_overtime"/>
            <field name="binding_model_id" ref="printing_sale_overtime.model_sale_overtime"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
                if records:
                    for rec in records:
                        action = rec.action_register_multiple_overtime_payment(records=records)
            </field>
    </record>

</odoo>
