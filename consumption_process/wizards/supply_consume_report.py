# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
import base64

import xlsxwriter
from odoo import fields, models


class SupplyConsumption(models.TransientModel):
    _inherit = "supply.consumption.wizard"

    def get_onscreen_new_report_for_consumption(self):
        """
        Consumption Report for Expense Account
        """
        query = """delete from consumption_report_onscreen_tb"""
        self._cr.execute(query)
        account_move_ids = []
        consumption_report = self.env["consumption.report.onscreen.tb"]
        domain = [("state", "=", "confirm")]
        if self.sale_ids:
            domain.append(("sale_id", "in", self.sale_ids.ids))
        if self.user_ids:
            domain.append(("create_uid", "in", self.user_ids.ids))
        if self.location_id:
            domain.append(("consume_location_id", "=", self.location_id.id))
        supply_material_consume_ids = self.env["supply.material.consume"].search(domain)
        if supply_material_consume_ids:
            move_line_domain = [
                ("account_id.user_type_id.internal_group", "=", "expense"),
                ("move_id.state", "=", "posted"),
                "|",
                ("supply_consume_id", "!=", False),
                ("scrap_stock_move_id", "!=", False),
                ("company_id", "=", self.company_id.id)
            ]
            if self.date_start:
                move_line_domain.append(("date", ">=", self.date_start))
            if self.date_end:
                move_line_domain.append(("date", "<=", self.date_end))
            if self.sale_ids:
                move_line_domain.append(("sale_id", "in", self.sale_ids.ids))
            account_move_line_ids = self.env["account.move.line"].sudo().search(move_line_domain)
            for move_line in account_move_line_ids:
                vals = {
                    "supply_req_no": move_line.supply_consume_id.name
                    if move_line.supply_consume_id else move_line.scrap_stock_move_id.origin,
                    "date": move_line.date,
                    "product_id": move_line.product_id.id if move_line.product_id else False,
                    "default_code": move_line.product_id.default_code if move_line.product_id else False,
                    "qty_consumed": move_line.quantity if not move_line.scrap_stock_move_id else 0.0,
                    "qty_damaged": move_line.scrap_stock_move_id.product_uom_qty,
                    "account_id": move_line.account_id.id if move_line.account_id else False,
                    "move_id": move_line.move_id.id,
                    "unit_cost": move_line.price_unit or 0.0,
                    "unit_total": -move_line.balance or 0.0,
                    "location_id": move_line.move_id.stock_move_id.location_id.id,
                    "location_dest_id": move_line.move_id.stock_move_id.location_dest_id.id,
                    "requested_id": move_line.supply_consume_id.task_id.printing_completed_by_id.id
                    if move_line.supply_consume_id and move_line.supply_consume_id.task_id else False,
                    "approved_id": move_line.supply_consume_id.supply_consume_picking_ids[0].assigned_to.id
                    if move_line.supply_consume_id.supply_consume_picking_ids else False,
                    "user_id": move_line.move_id.create_uid.id,
                    "sale_id": move_line.sale_id.id
                }
                consumption_report.sudo().create(vals)
            action = self.env.ref(
                "supply_consume_management.action_consumption_report_onscreen_report"
            ).read()[0]
            action["context"] = {"from_date": self.date_start, "to_date": self.date_end}
            action["target"] = "main"
            return action
        return False

    # def get_onscreen_report_for_consumption(self):
    #     """override for add sale domain in query
    #     todo no need to override we need to do domain in sperate method
    #     """
    #     query = """delete from consumption_report_onscreen_tb"""
    #     self._cr.execute(query)
    #     consumption_report = self.env["consumption.report.onscreen.tb"]
    #     # location_ids = self.env["stock.location"].search(
    #     #     [
    #     #         ("usage", "=", "customer"),
    #     #         ("is_consume_loc", '=', True),
    #     #         ("valuation_in_account_id", "!=", False),
    #     #         ("valuation_out_account_id", "!=", False),
    #     #         ("cost_of_goods_account_id", "!=", False),
    #     #     ]
    #     # )
    #     #
    #     # if self.location_id:
    #     #     location_ids = self.location_id
    #     # valuation_account_ids = location_ids.mapped("valuation_in_account_id")
    #     # cost_of_goods_account_ids = location_ids.mapped("cost_of_goods_account_id")
    #     # account_ids = valuation_account_ids + cost_of_goods_account_ids
    #
    #     account_ids = False
    #     supply_material_consume = self.env["supply.material.consume"].search(
    #         [
    #             ("state", "=", "confirm"),
    #             ("company_id", "=", self.env.company.id),
    #             ("creation_date", ">=", self.date_start),
    #             ("creation_date", "<=", self.date_end)
    #         ]
    #     )
    #     if supply_material_consume:
    #         valuation_account_ids = supply_material_consume.mapped("consume_location_id.valuation_in_account_id")
    #         cost_of_goods_account_ids = supply_material_consume.mapped("consume_location_id.cost_of_goods_account_id")
    #         account_ids = valuation_account_ids + cost_of_goods_account_ids
    #
    #     if self.location_id:
    #         account_ids = self.location_id.mapped("valuation_in_account_id") + self.location_id.mapped("cost_of_goods_account_id")
    #
    #     args = [tuple(account_ids.ids)]
    #     query = """
    #             select aml.move_id, aml.product_id, aml.parent_state,
    #             aml.date, aml.id, aml.debit, aml.credit, aml.name
    #             from account_move_line as aml
    #             LEFT JOIN sp_task_process task ON task.id = aml.task_id
    #             inner join account_move mv on mv.id = aml.move_id
    #             inner join stock_move sm on sm.id = mv.stock_move_id
    #             inner join stock_location sl on sl.id = sm.location_id
    #             inner join stock_location dsl on dsl.id = sm.location_dest_id
    #             where aml.parent_state = 'posted'
    #             """
    #     # todo need to fix because if account is single then it was add , in last
    #     if len(account_ids) == 1:
    #         query += """ and aml.account_id = {} and aml.product_id is not null """.format(account_ids.id)
    #     else:
    #         query +=""" and aml.account_id in {} and aml.product_id is not null """.format(tuple(account_ids.ids))
    #     if self.date_start:
    #         query += """ and aml.date >= '{}' """.format(self.date_start)
    #     if self.date_end:
    #         query += """ and aml.date <= '{}' """.format(self.date_end)
    #     if self.user_ids:
    #         if len(self.user_ids) == 1:
    #             query += """ and aml.create_uid = {}""".format(self.user_ids.id)
    #         else:
    #             query += """ and aml.create_uid in {}""".format(tuple(self.user_ids.ids))
    #     if self.location_id:
    #         query += """ and (sm.location_id = {} or sm.location_dest_id = {}) """.format(self.location_id.id, self.location_id.id)
    #     if self.sale_ids:
    #         if len(self.sale_ids) == 1:
    #             query += """ AND (aml.sale_id = {} OR task.sale_id = {})""".format(self.sale_ids.id, self.sale_ids.id)
    #         else:
    #             query += """ AND (aml.sale_id IN {} OR task.sale_id IN {})""".format(tuple(self.sale_ids.ids),
    #                                                                                  tuple(self.sale_ids.ids))
    #     self.env.cr.execute(query)
    #     result_account_move_line = self.env.cr.fetchall()
    #     list_details = []
    #     for line in result_account_move_line:
    #         move_line_value = -1 * line[6]
    #         if line[5] > 0.0:
    #             move_line_value = line[5]
    #         product_id = self.env["product.product"].browse(line[1])
    #         move_id = self.env["account.move"].browse(line[0])
    #         move_line_id = self.env["account.move.line"].browse(line[4])
    #         valuation_id = self.env["stock.valuation.layer"].search(
    #             [("account_move_id", "=", line[0])]
    #         )
    #         stock_move_id = valuation_id.stock_move_id
    #         if not stock_move_id:
    #             continue
    #         quantity = 0.0
    #         unit_cost = 0.0
    #         if move_line_id.quantity:
    #             quantity = move_line_id.quantity
    #             unit_cost = move_line_value / move_line_id.quantity
    #         if stock_move_id:
    #             quantity = stock_move_id.product_uom_qty
    #             unit_cost = move_line_value / stock_move_id.product_uom_qty
    #         ref_name = stock_move_id.picking_id and \
    #                    stock_move_id.picking_id.supply_material_consume_id.sudo().name or False
    #         src_location = stock_move_id.location_id.id
    #         if stock_move_id.supply_req_id:
    #             src_location = stock_move_id.supply_req_id.source_location_id.id
    #         requested_by_id = False
    #         req_by = False
    #         approve_by = False
    #         if stock_move_id.picking_id and stock_move_id.picking_id.requested_by:
    #             req_by = stock_move_id.picking_id.requested_by.id
    #         else:
    #             if stock_move_id.supply_req_id:
    #                 req_by = stock_move_id.supply_req_id.requested_by.id
    #         if stock_move_id.picking_id and stock_move_id.picking_id.assigned_to:
    #             approve_by = stock_move_id.picking_id.assigned_to.id
    #         else:
    #             if stock_move_id.supply_req_id:
    #                 approve_by = stock_move_id.supply_req_id.assigned_to.id
    #         supply_consume_id = self.env["supply.material.consume"].sudo().search(
    #             [("name", "=", ref_name), ("company_id", "=", stock_move_id.company_id.id)]
    #         )
    #         vals = {
    #             "supply_req_no": ref_name if ref_name else stock_move_id.origin,
    #             "date": line[3],
    #             "product_id": product_id.id,
    #             "default_code": product_id.default_code,
    #             "qty_consumed": quantity,
    #             "qty_damaged": 0.0,
    #             "account_id": move_line_id.account_id.id,
    #             "move_id": move_line_id.move_id.id,
    #             "unit_cost": unit_cost or 0.0,
    #             "unit_total": move_line_value or 0.0,
    #             "location_id": src_location,
    #             "location_dest_id": stock_move_id.location_dest_id.id,
    #             "requested_id": stock_move_id.task_id.printing_completed_by_id.id,
    #             "approved_id": approve_by,
    #             "user_id": move_id.create_uid.id,
    #             "sale_id": move_line_id.sale_id.id if move_line_id.sale_id else supply_consume_id.sale_id.id
    #         }
    #         list_details.append(vals)
    #     consumption_report.sudo().create(list_details)
    #     action = self.env.ref(
    #         "supply_consume_management.action_consumption_report_onscreen_report"
    #     ).read()[0]
    #     action["context"] = {"from_date": self.date_start, "to_date": self.date_end}
    #     action["target"] = "main"
    #     return action


    def get_onscreen_report_for_consumption(self):
        """
        Consumption Report for Expense Account
        """
        query = """delete from consumption_report_onscreen_tb"""
        self._cr.execute(query)
        consumption_report = self.env["consumption.report.onscreen.tb"]
        domain = [
            ("consume_id.state", "=", "confirm"),
            ("consume_id.company_id", "=", self.env.company.id),
        ]
        if self.date_start:
            domain.append(("consume_id.creation_date", ">=", self.date_start))
        if self.date_end:
            domain.append(("consume_id.creation_date", "<=", self.date_end))
        if self.sale_ids:
            domain.append(("consume_id.sale_id", "in", self.sale_ids.ids))
        # if self.user_ids:
        #     domain.append(("consume_id.user_id", "in", self.user_ids.ids))
        if self.location_id:
            domain.append(("consume_id.consume_location_id", "=", self.location_id.id))

        supply_material_consume_lines = self.env["supply.material.consume.line"].search(domain)
        if supply_material_consume_lines:
            for consume_line in supply_material_consume_lines:
                if consume_line.consume_id.sale_id.printing_sale_status == "done":
                    ref_name = consume_line.consume_id.name
                    req_by = False
                    approve_by = False
                    if consume_line.consume_id.consumed_by:
                        req_by = consume_line.consume_id.consumed_by.id
                    stock_move_ids = self.env["stock.move"].sudo().search([
                        ("picking_id.supply_material_consume_id", "=", consume_line.consume_id.id),
                        ("location_dest_id", "=", consume_line.consume_id.consume_location_id.id)
                    ])
                    for stock_move in stock_move_ids:
                        account_moves = self.env["account.move"].sudo().search([
                            ("stock_move_id", "=", stock_move.id),
                            ("state", "=", "posted")
                        ])
                        if account_moves:
                            for move in account_moves:
                                for move_line in move.line_ids:
                                    if (move_line.account_id == move_line.move_id.stock_move_id.location_dest_id.valuation_in_account_id
                                            or move_line.account_id == move_line.move_id.stock_move_id.location_dest_id.current_asset_account_id):
                                        account_id = consume_line.consume_id.consume_location_id.valuation_in_account_id
                                        if (consume_line.consume_id.sale_id.sale_consumption_line
                                            or consume_line.consume_id.sale_id.remaining_consume_balance_move_ids
                                            or consume_line.consume_id.sale_id.gift_consumption_move_ids):
                                            account_id = consume_line.consume_id.consume_location_id.cost_of_goods_account_id
                                        vals = {
                                            "supply_req_no": ref_name if ref_name else stock_move.origin,
                                            "date": move_line.date,
                                            "product_id": move_line.product_id.id if move_line.product_id else False,
                                            "default_code": move_line.product_id.default_code if move_line.product_id else False,
                                            "qty_consumed": move_line.move_id.stock_move_id.product_uom_qty,
                                            "qty_damaged": 0.0,
                                            "account_id": account_id.id if account_id else False,
                                            "move_id": move_line.move_id.id,
                                            "unit_cost": (move_line.debit - move_line.credit) / move_line.move_id.stock_move_id.product_uom_qty or 0.0,
                                            "unit_total": move_line.balance or 0.0,
                                            "location_id": move_line.move_id.stock_move_id.location_id.id,
                                            "location_dest_id": move_line.move_id.stock_move_id.location_dest_id.id,
                                            "requested_id": req_by,
                                            "approved_id": approve_by,
                                            "user_id": move_line.move_id.create_uid.id,
                                            "sale_id": consume_line.consume_id.sale_id.id
                                        }
                                        consumption_report.sudo().create(vals)
            action = self.env.ref(
                "supply_consume_management.action_consumption_report_onscreen_report"
            ).read()[0]
            action["context"] = {"from_date": self.date_start, "to_date": self.date_end}
            action["target"] = "main"
            return action
        return False
