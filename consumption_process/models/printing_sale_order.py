# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/

from odoo import _, api, fields, models
from odoo.exceptions import UserError
from collections import defaultdict


class PrintingSaleOrder(models.Model):
    _inherit = "sale.order"

    sale_consumption_line = fields.One2many(
        "task.consumption.line", "sale_id", string="Consumption Line"
    )
    total_consumed_qty = fields.Float(string="Total Consumed Quantity")
    remaining_consume_balance_move_ids = fields.Many2many(
        "account.move",
        "consumption_remaining_account_move_rel",
        string="Remaining Consumption Balance Move"
    )
    damaged_consumed_qty = fields.Float(string="Damaged Consumed Qty")
    gift_consumption_move_ids = fields.Many2many(
        "account.move", "gift_consumption_move_rel", string="Gift Consumption Line"
    )

    def prepare_debit_line_consumption_vals(self, account_id, line, scrap_stock_move_id=False, gift_sample=False):
        if not line.credit:  # Skip if credit is 0.0
            return False
        debit_vals = {
            'name': 'Consumption Debit Line for %s' % line.product_id.name,
            'product_id': line.product_id.id,
            'supply_consume_id': line.move_id.stock_move_id.picking_id.supply_material_consume_id.id,
            'debit': line.credit,
            'credit': 0.0,
            'account_id': account_id,
            'quantity': line.move_id.stock_move_id.product_uom_qty,
            'price_unit': line.credit / line.move_id.stock_move_id.product_uom_qty,
            'date': fields.Date.today(),
            'move_id': line.move_id.id,
            'partner_id': line.move_id.partner_id.id,
            'currency_id': line.move_id.currency_id.id,
            'exclude_from_invoice_tab': True,
            'sale_id': self.id,
            'branch_id': self.branch_id.id if self.branch_id else False,
            'task_consumption': True,
            'scrap_stock_move_id': scrap_stock_move_id.id if scrap_stock_move_id else False,
            'from_location_id': scrap_stock_move_id.location_id.id if scrap_stock_move_id
            else line.move_id.stock_move_id.location_id.id,
            'consume_location_id': scrap_stock_move_id.location_dest_id.id if scrap_stock_move_id
            else line.move_id.stock_move_id.location_dest_id.id,
            'gift_sample_move': gift_sample,
        }
        return debit_vals

    def prepare_credit_line_consumption_vals(self, account_id, line, scrap_stock_move_id=False, gift_sample=False):
        if not line.credit:  # Skip if credit is 0.0
            return False
        credit_vals = {
            'name': 'Consumption Credit Line for %s' % line.product_id.name,
            'product_id': line.product_id.id,
            'supply_consume_id': line.move_id.stock_move_id.picking_id.supply_material_consume_id.id,
            'debit': 0.0,
            'credit': line.credit,
            'account_id': account_id,
            'quantity': line.move_id.stock_move_id.product_uom_qty,
            'price_unit': line.credit / line.move_id.stock_move_id.product_uom_qty,
            'date': fields.Date.today(),
            'move_id': line.move_id.id,
            'partner_id': line.move_id.partner_id.id,
            'currency_id': line.move_id.currency_id.id,
            'exclude_from_invoice_tab': True,
            'sale_id': self.id,
            'branch_id': self.branch_id.id if self.branch_id else False,
            'task_consumption': True,
            'scrap_stock_move_id': scrap_stock_move_id.id if scrap_stock_move_id else False,
            'from_location_id': scrap_stock_move_id.location_id.id if scrap_stock_move_id
            else line.move_id.stock_move_id.location_id.id,
            'consume_location_id': scrap_stock_move_id.location_dest_id.id if scrap_stock_move_id
            else line.move_id.stock_move_id.location_dest_id.id,
            'gift_sample_move': gift_sample,
        }
        return credit_vals

    def _create_invoices(self, grouped=False, final=False, date=None):
        """
        Inherit to add consumption lines in invoice
        """
        invoices = super(PrintingSaleOrder, self)._create_invoices(grouped=grouped, final=final, date=date)
        if invoices:
            for invoice in invoices:
                if self.sale_consumption_line:
                    move_lines = []
                    invoice_line_consumption_ids = self.env["account.move.line"].sudo().search([
                        ("move_id.sale_id", "=", self.id),
                        ("supply_consume_id", "!=", False),
                        ("move_id.state", "=", "posted"),
                    ])
                    supply_material_consume_line_ids = invoice_line_consumption_ids.mapped("supply_consume_id")
                    supply_material_consumes = self.env["supply.material.consume"].sudo().search([
                        ("sale_id", "=", self.id),
                        ("state", "=", "confirm"),
                        ("id", "not in", supply_material_consume_line_ids.ids)
                    ])
                    if supply_material_consumes:
                        account_move_line_ids = self.env["account.move.line"].sudo().search([
                            ("move_id.stock_move_id.picking_id.supply_material_consume_id", "in", supply_material_consumes.ids),
                            ("move_id.state", "=", "posted"),
                            ("move_id.move_type", "=", "entry"),
                            ("account_id.user_type_id.internal_group", "=", "asset"),
                            ("company_id", "=", self.company_id.id)
                        ])
                        if account_move_line_ids:
                            for line in account_move_line_ids:
                                if line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id == line.move_id.stock_move_id.location_dest_id:
                                    debit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.cost_of_goods_account_id.id
                                    credit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.current_asset_account_id.id
                                else:
                                    debit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.current_asset_account_id.id
                                    credit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.cost_of_goods_account_id.id
                                debit_move_line = self.prepare_debit_line_consumption_vals(
                                    debit_account_id, line
                                )
                                if debit_move_line:  # Only append if valid
                                    move_lines.append((0, 0, debit_move_line))
                                credit_move_line = self.prepare_credit_line_consumption_vals(
                                    credit_account_id, line
                                )
                                if credit_move_line:  # Only append if valid
                                    move_lines.append((0, 0, credit_move_line))

                    scrap_move_ids = self.env["stock.move"].sudo().search(
                        [
                            ("task_id.sale_id", "=", self.id),
                            ("scrap_id", "!=", False),
                            ("location_dest_id.scrap_location", "!=", False),
                        ]
                    )
                    if scrap_move_ids:
                        scrap_account_move_line_ids = self.env["account.move.line"].sudo().search([
                            ("move_id.stock_move_id", "in", scrap_move_ids.ids),
                            ("move_id.state", "=", "posted"),
                            ("move_id.move_type", "=", "entry"),
                            ("account_id.user_type_id.internal_group", "=", "asset"),
                            ("company_id", "=", self.company_id.id)
                        ])
                        if scrap_account_move_line_ids:
                            for line in scrap_account_move_line_ids:
                                debit_account_id = line.move_id.stock_move_id.location_dest_id.cost_of_goods_account_id.id
                                credit_account_id = line.move_id.stock_move_id.location_dest_id.current_asset_account_id.id
                                debit_move_line = self.prepare_debit_line_consumption_vals(
                                    debit_account_id, line, scrap_stock_move_id=line.move_id.stock_move_id
                                )
                                if debit_move_line:
                                    move_lines.append((0, 0, debit_move_line))
                                credit_move_line = self.prepare_credit_line_consumption_vals(
                                    credit_account_id, line, scrap_stock_move_id=line.move_id.stock_move_id
                                )
                                if credit_move_line:
                                    move_lines.append((0, 0, credit_move_line))
                    if move_lines:
                        invoice.update({"line_ids": move_lines})
                        invoice.date = fields.Date.today()
                        invoice.invoice_date = fields.Date.today()
                        invoice.line_ids.update({"date": fields.Date.today()})
                        invoice.invoice_line_ids.update({"date": fields.Date.today()})
        return invoices

    def create_move_vals_for_gift_sample(self, move_lines):
        if move_lines:
            move_vals = {
                'partner_id': self.partner_id.id,
                'ref': f"Consumption for Gift/Sample Products in Sale Order {self.name}",
                'journal_id': self.env['account.journal'].search(
                    [('type', '=', 'general'), ('company_id', '=', self.company_id.id)], limit=1).id,
                'date': fields.Date.today(),
                'company_id': self.company_id.id,
                'sale_id': self.id,
                'gift_sample_move': True,
                'line_ids': move_lines,
            }
            account_move = self.env['account.move'].sudo().create(move_vals)
            account_move.gift_sample_move = True
            account_move.action_post()
            self.gift_consumption_move_ids |= account_move
            return account_move

    def create_gift_sample_consumption_account_move(self):
        if self.sale_consumption_line:
            move_lines = []
            supply_material_consumes = self.env["supply.material.consume"].sudo().search([
                ("sale_id", "=", self.id),
                ("state", "=", "confirm"),
                "|",
                ("task_id.sale_line_id.gift_product", "=", True),
                ("task_id.sale_line_id.sample_product", "=", True)
            ])
            if supply_material_consumes:
                account_move_line_ids = self.env["account.move.line"].sudo().search([
                    ("move_id.stock_move_id.picking_id.supply_material_consume_id", "in", supply_material_consumes.ids),
                    ("move_id.state", "=", "posted"),
                    ("move_id.move_type", "=", "entry"),
                    ("account_id.user_type_id.internal_group", "=", "asset"),
                    ("company_id", "=", self.company_id.id)
                ])
                if account_move_line_ids:
                    for line in account_move_line_ids:
                        if line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id == line.move_id.stock_move_id.location_dest_id:
                            debit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.cost_of_goods_account_id.id
                            credit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.current_asset_account_id.id
                        else:
                            debit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.current_asset_account_id.id
                            credit_account_id = line.move_id.stock_move_id.picking_id.supply_material_consume_id.consume_location_id.cost_of_goods_account_id.id
                        debit_move_line = self.prepare_debit_line_consumption_vals(
                            debit_account_id, line, gift_sample=True
                        )
                        if debit_move_line:  # Only append if valid
                            move_lines.append((0, 0, debit_move_line))
                        credit_move_line = self.prepare_credit_line_consumption_vals(
                            credit_account_id, line, gift_sample=True
                        )
                        if credit_move_line:  # Only append if valid
                            move_lines.append((0, 0, credit_move_line))

            scrap_move_ids = self.env["stock.move"].sudo().search(
                [
                    ("task_id.sale_id", "=", self.id),
                    ("scrap_id", "!=", False),
                    ("location_dest_id.scrap_location", "!=", False),
                ]
            )
            if scrap_move_ids:
                scrap_account_move_line_ids = self.env["account.move.line"].sudo().search([
                    ("move_id.stock_move_id", "in", scrap_move_ids.ids),
                    ("move_id.state", "=", "posted"),
                    ("move_id.move_type", "=", "entry"),
                    ("account_id.user_type_id.internal_group", "=", "asset"),
                    ("company_id", "=", self.company_id.id)
                ])
                if scrap_account_move_line_ids:
                    for line in scrap_account_move_line_ids:
                        debit_account_id = line.move_id.stock_move_id.location_dest_id.cost_of_goods_account_id.id
                        credit_account_id = line.move_id.stock_move_id.location_dest_id.current_asset_account_id.id
                        debit_move_line = self.prepare_debit_line_consumption_vals(
                            debit_account_id, line, scrap_stock_move_id=line.move_id.stock_move_id, gift_sample=True
                        )
                        if debit_move_line:  # Only append if valid
                            move_lines.append((0, 0, debit_move_line))
                        credit_move_line = self.prepare_credit_line_consumption_vals(
                            credit_account_id, line, scrap_stock_move_id=line.move_id.stock_move_id, gift_sample=True
                        )
                        if credit_move_line:  # Only append if valid
                            move_lines.append((0, 0, credit_move_line))
            if move_lines:
                self.create_move_vals_for_gift_sample(move_lines)

    # def _create_invoices(self, grouped=False, final=False, date=None):
    #     """
    #     Override invoice creation to add journal entry lines (line_ids) for consumption lines.
    #     Consolidate credit lines if all stock valuation accounts are the same, otherwise create separate lines.
    #     Consolidate debit lines if all cost of goods accounts are the same, otherwise create separate lines.
    #     """
    #     # Call the parent method with the correct date parameter
    #     invoices = super(PrintingSaleOrder, self)._create_invoices(grouped=grouped, final=final, date=date)
    #
    #     for invoice in invoices:
    #         if not self.sale_consumption_line:
    #             continue
    #
    #         debit_lines = defaultdict(float)
    #         credit_lines = defaultdict(float)
    #         stock_valuation_accounts = set()
    #         cost_of_goods_accounts = set()
    #         for consumption_line in self.sale_consumption_line:
    #             if (consumption_line.consumed_qty > consumption_line.qty_invoiced
    #                     and not (consumption_line.sale_line_id.gift_product
    #                              or consumption_line.sale_line_id.sample_product)) :
    #                 qty_to_invoice = consumption_line.consumed_qty - consumption_line.qty_invoiced
    #                 to_location = consumption_line.to_location_id
    #                 product = consumption_line.product_id
    #
    #                 if not to_location:
    #                     raise UserError(_("To Location is not set for consumption line."))
    #
    #                 # Get accounts
    #                 stock_valuation_account = to_location.current_asset_account_id
    #                 cost_of_goods_account = to_location.cost_of_goods_account_id
    #
    #                 if not stock_valuation_account or not cost_of_goods_account:
    #                     raise UserError(
    #                         _("Stock Valuation Account or Cost of Goods Account is not set for location %s.")
    #                         % to_location.name
    #                     )
    #
    #                 # Track unique accounts
    #                 stock_valuation_accounts.add(stock_valuation_account.id)
    #                 cost_of_goods_accounts.add(cost_of_goods_account.id)
    #
    #                 # Calculate amount from journal entry linked to stock move
    #                 amount = 0.0
    #                 stock_move_ids = self.env["stock.move"].sudo().search([
    #                     ("task_id", "=", consumption_line.task_id.id),
    #                     ("location_dest_id", "=", consumption_line.to_location_id.id)
    #                 ])
    #                 if stock_move_ids:
    #                     for stock_move in stock_move_ids:
    #                         account_move = self.env["account.move"].sudo().search([
    #                             ("stock_move_id", "=", stock_move.id)
    #                         ])
    #                         if account_move:
    #                             amount += sum(move.amount_total for move in account_move)
    #
    #                 # Accumulate amounts for debit and credit lines
    #                 debit_lines[cost_of_goods_account.id] += amount
    #                 credit_lines[stock_valuation_account.id] += amount
    #
    #                 # Update qty_invoiced
    #                 consumption_line.qty_invoiced += qty_to_invoice
    #                 invoice.task_consumption_line_ids |= consumption_line
    #
    #         # Prepare new journal entry lines
    #         move_lines = []
    #
    #         # Handle debit lines (Cost of Goods Sold)
    #         if len(cost_of_goods_accounts) == 1:
    #             # All cost of goods accounts are the same, consolidate into one debit line
    #             account_id = next(iter(cost_of_goods_accounts))
    #             total_amount = sum(debit_lines.values())  # Sum all amounts
    #             move_lines.append((0, 0, {
    #                 'name': _("Cost of Goods Sold - Consumption"),
    #                 'account_id': account_id,
    #                 'debit': total_amount,
    #                 'credit': 0.0,
    #                 'move_id': invoice.id,
    #                 'partner_id': invoice.partner_id.id if invoice.partner_id else False,
    #                 'currency_id': invoice.currency_id.id if invoice.currency_id else False,
    #                 'exclude_from_invoice_tab': True,
    #                 'sale_id': self.id,
    #                 'branch_id': self.branch_id.id if self.branch_id else False,
    #                 'task_consumption': True,
    #             }))
    #         else:
    #             # Different cost of goods accounts, create separate debit lines
    #             for account_id, amount in debit_lines.items():
    #                 move_lines.append((0, 0, {
    #                     'name': _("Cost of Goods Sold - Consumption"),
    #                     'account_id': account_id,
    #                     'debit': amount,
    #                     'credit': 0.0,
    #                     'move_id': invoice.id,
    #                     'partner_id': invoice.partner_id.id if invoice.partner_id else False,
    #                     'currency_id': invoice.currency_id.id if invoice.currency_id else False,
    #                     'exclude_from_invoice_tab': True,
    #                     'sale_id': self.id,
    #                     'branch_id': self.branch_id.id if self.branch_id else False,
    #                     'task_consumption': True,
    #                 }))
    #
    #         # Handle credit lines (Stock Valuation Adjustment)
    #         if len(stock_valuation_accounts) == 1:
    #             # All stock valuation accounts are the same, consolidate into one credit line
    #             account_id = next(iter(stock_valuation_accounts))
    #             total_amount = sum(credit_lines.values())  # Sum all amounts
    #             move_lines.append((0, 0, {
    #                 'name': _("Stock Valuation Adjustment - Consumption"),
    #                 'account_id': account_id,
    #                 'debit': 0.0,
    #                 'credit': total_amount,
    #                 'move_id': invoice.id,
    #                 'partner_id': invoice.partner_id.id if invoice.partner_id else False,
    #                 'currency_id': invoice.currency_id.id if invoice.currency_id else False,
    #                 'exclude_from_invoice_tab': True,
    #                 'sale_id': self.id,
    #                 'branch_id': self.branch_id.id if self.branch_id else False,
    #                 'task_consumption': True,
    #             }))
    #         else:
    #             # Different stock valuation accounts, create separate credit lines
    #             for account_id, amount in credit_lines.items():
    #                 move_lines.append((0, 0, {
    #                     'name': _("Stock Valuation Adjustment - Consumption"),
    #                     'account_id': account_id,
    #                     'debit': 0.0,
    #                     'credit': amount,
    #                     'move_id': invoice.id,
    #                     'partner_id': invoice.partner_id.id if invoice.partner_id else False,
    #                     'currency_id': invoice.currency_id.id if invoice.currency_id else False,
    #                     'exclude_from_invoice_tab': True,
    #                     'sale_id': self.id,
    #                     'branch_id': self.branch_id.id if self.branch_id else False,
    #                     'task_consumption': True,
    #                 }))
    #
    #         # Write journal entry lines to the invoice
    #         if move_lines:
    #             invoice.sudo().write({'line_ids': move_lines})
    #
    #     return invoices

    # def create_gift_sample_consumption_account_move(self):
    #     """Create a single accounting entry for gift and sample product consumptions."""
    #     self.ensure_one()  # Ensure called on a single record
    #     if not self.sale_consumption_line:
    #         return False
    #
    #     # Initialize structures for grouping lines
    #     debit_lines = defaultdict(float)  # {account_id: total_amount}
    #     credit_lines = defaultdict(float)  # {account_id: total_amount}
    #     stock_valuation_accounts = set()  # Track unique stock valuation accounts
    #     cost_of_goods_accounts = set()  # Track unique cost of goods accounts
    #     journal_id = None
    #     processed_consumption_lines = []  # Track lines to update qty_invoiced
    #     last_product = None  # Track last product for journal entry lines
    #     for consumption_line in self.sale_consumption_line:
    #         # Check if consumed_qty > qty_invoiced and include only gift/sample products
    #         consumption_line.qty_invoiced = 0.0
    #         if (consumption_line.consumed_qty > consumption_line.qty_invoiced
    #                 and (consumption_line.sale_line_id.gift_product
    #                      or consumption_line.sale_line_id.sample_product)):
    #             qty_to_invoice = consumption_line.consumed_qty - consumption_line.qty_invoiced
    #             to_location = consumption_line.to_location_id
    #             product = consumption_line.product_id
    #             last_product = product  # Update last product
    #
    #             if not to_location:
    #                 raise UserError(_("Destination Location is not set for consumption line."))
    #
    #             # Get accounts from location
    #             stock_valuation_account = to_location.current_asset_account_id
    #             cost_of_goods_account = to_location.cost_of_goods_account_id
    #
    #             if not stock_valuation_account or not cost_of_goods_account:
    #                 raise UserError(
    #                     _("Stock Valuation Account or Cost of Goods Account is not set for location %s.")
    #                     % to_location.name
    #                 )
    #
    #             # Track unique accounts
    #             stock_valuation_accounts.add(stock_valuation_account.id)
    #             cost_of_goods_accounts.add(cost_of_goods_account.id)
    #
    #             # Calculate amount from journal entry linked to stock move
    #             amount = 0.0
    #             stock_move_ids = self.env["stock.move"].sudo().search([
    #                 ("task_id", "=", consumption_line.task_id.id),
    #                 ("location_dest_id", "=", consumption_line.to_location_id.id),
    #                 ("company_id", "=", self.company_id.id),
    #             ])
    #             if stock_move_ids:
    #                 for stock_move in stock_move_ids:
    #                     account_move = self.env["account.move"].sudo().search([
    #                         ("stock_move_id", "=", stock_move.id),
    #                         ("state", "=", "posted")
    #                     ])
    #                     if account_move:
    #                         amount = sum(move.amount_total for move in account_move)
    #                         if not journal_id:
    #                             journal_id = account_move[0].journal_id
    #
    #             if amount > 0.0:
    #                 # Accumulate amounts for debit and credit lines
    #                 debit_lines[cost_of_goods_account.id] += amount
    #                 credit_lines[stock_valuation_account.id] += amount
    #                 processed_consumption_lines.append((consumption_line, qty_to_invoice))
    #
    #     # Prepare journal entry lines
    #     move_lines = []
    #
    #     # Handle debit lines (Cost of Goods Sold)
    #     if cost_of_goods_accounts:  # Ensure there are accounts to process
    #         if len(cost_of_goods_accounts) == 1:
    #             # All cost of goods accounts are the same, create one combined debit line
    #             account_id = next(iter(cost_of_goods_accounts))
    #             total_amount = sum(debit_lines.values())  # Sum all debit amounts
    #             move_lines.append((0, 0, {
    #                 'name': _("Cost of Goods Sold - Consumption"),
    #                 'account_id': account_id,
    #                 'debit': total_amount,
    #                 'credit': 0.0,
    #                 'partner_id': self.partner_id.id if self.partner_id else False,
    #                 'currency_id': self.currency_id.id if self.currency_id else False,
    #                 'exclude_from_invoice_tab': True,
    #                 'sale_id': self.id,
    #                 'branch_id': self.branch_id.id if self.branch_id else False,
    #                 'task_consumption': True,
    #                 'product_id': last_product.id if last_product else False,
    #                 'quantity': sum(qty for _, qty in processed_consumption_lines),
    #                 'product_uom_id': last_product.uom_id.id if last_product else False,
    #             }))
    #         else:
    #             # Different cost of goods accounts, create separate debit lines
    #             for account_id, amount in debit_lines.items():
    #                 move_lines.append((0, 0, {
    #                     'name': _("Cost of Goods Sold - Consumption"),
    #                     'account_id': account_id,
    #                     'debit': amount,
    #                     'credit': 0.0,
    #                     'partner_id': self.partner_id.id if self.partner_id else False,
    #                     'currency_id': self.currency_id.id if self.currency_id else False,
    #                     'exclude_from_invoice_tab': True,
    #                     'sale_id': self.id,
    #                     'branch_id': self.branch_id.id if self.branch_id else False,
    #                     'task_consumption': True,
    #                     'product_id': last_product.id if last_product else False,
    #                     'quantity': sum(qty for _, qty in processed_consumption_lines),
    #                     'product_uom_id': last_product.uom_id.id if last_product else False,
    #                 }))
    #
    #     # Handle credit lines (Stock Valuation Adjustment)
    #     if stock_valuation_accounts:  # Ensure there are accounts to process
    #         if len(stock_valuation_accounts) == 1:
    #             # All stock valuation accounts are the same, create one combined credit line
    #             account_id = next(iter(stock_valuation_accounts))
    #             total_amount = sum(credit_lines.values())  # Sum all credit amounts
    #             move_lines.append((0, 0, {
    #                 'name': _("Stock Valuation Adjustment - Consumption"),
    #                 'account_id': account_id,
    #                 'debit': 0.0,
    #                 'credit': total_amount,
    #                 'partner_id': self.partner_id.id if self.partner_id else False,
    #                 'currency_id': self.currency_id.id if self.currency_id else False,
    #                 'exclude_from_invoice_tab': True,
    #                 'sale_id': self.id,
    #                 'branch_id': self.branch_id.id if self.branch_id else False,
    #                 'task_consumption': True,
    #                 'product_id': last_product.id if last_product else False,
    #                 'quantity': sum(qty for _, qty in processed_consumption_lines),
    #                 'product_uom_id': last_product.uom_id.id if last_product else False,
    #             }))
    #         else:
    #             # Different stock valuation accounts, create separate credit lines
    #             for account_id, amount in credit_lines.items():
    #                 move_lines.append((0, 0, {
    #                     'name': _("Stock Valuation Adjustment - Consumption"),
    #                     'account_id': account_id,
    #                     'debit': 0.0,
    #                     'credit': amount,
    #                     'partner_id': self.partner_id.id if self.partner_id else False,
    #                     'currency_id': self.currency_id.id if self.currency_id else False,
    #                     'exclude_from_invoice_tab': True,
    #                     'sale_id': self.id,
    #                     'branch_id': self.branch_id.id if self.branch_id else False,
    #                     'task_consumption': True,
    #                     'product_id': last_product.id if last_product else False,
    #                     'quantity': sum(qty for _, qty in processed_consumption_lines),
    #                     'product_uom_id': last_product.uom_id.id if last_product else False,
    #                 }))
    #
    #     # Create account.move if there are lines to process
    #     if move_lines:
    #         # Update qty_invoiced for processed consumption lines
    #         for consumption_line, qty in processed_consumption_lines:
    #             consumption_line.qty_invoiced += qty
    #
    #         move_vals = {
    #             'partner_id': self.partner_id.id,
    #             'ref': f"Consumption for Gift/Sample Products in Sale Order {self.name}",
    #             'journal_id': journal_id.id if journal_id else self.env['account.journal'].search([('type', '=', 'general'), ('company_id', '=', self.company_id.id)], limit=1).id,
    #             'date': fields.Date.today(),
    #             'company_id': self.company_id.id,
    #             'sale_id': self.id,
    #             'line_ids': move_lines,
    #         }
    #         account_move = self.env['account.move'].sudo().create(move_vals)
    #         account_move.gift_sample_move = True
    #         account_move.action_post()
    #         self.gift_consumption_move_ids |= account_move
    #         return account_move
    #     return False

    def action_close_printing_sale(self):
        """Close printing sale and create combined consumption accounting entry for gift/sample products."""
        if self.sale_consumption_line:
            self.create_gift_sample_consumption_account_move()
        return super().action_close_printing_sale()

    def action_view_gift_sample_accounting_entry(self):
        """Open a window to view gift/sample accounting entries."""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Gift/Sample Accounting Entries',
            'res_model': 'account.move',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.gift_consumption_move_ids.ids)],
            'context': {'default_sale_id': self.id},
            'target': 'current',
        }


class PrintingSaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    production_consumed_qty = fields.Float(string="Production Consumed Qty", store=True)
    workshop_consumed_qty = fields.Float(string="Workshop Consumed Qty", store=True)
    sticker_consumed_qty = fields.Float(string="Sticker Consumed Qty", store=True)
    production_damaged_qty = fields.Float(string="Production Damaged Qty", store=True)
    workshop_damaged_qty = fields.Float(string="Workshop Damaged Qty", store=True)
    sticker_damaged_qty = fields.Float(string=" Sticker Damaged Qty", store=True)


class ConsumptionLine(models.Model):
    _name = "task.consumption.line"

    sale_id = fields.Many2one("sale.order", string="Sale Order")
    sale_line_id = fields.Many2one("sale.order.line", string="Sale Order")
    task_id = fields.Many2one("sp.task.process", string="Task")
    order_task_type = fields.Selection(
        [("production", "Production"), ("delivery", "Delivery")],
        string="Task Type",
    )
    delivery_type = fields.Selection(
        [("workshop", "Workshop"), ("sticker", "Sticker")],
        string="Delivery Team",
    )
    from_location_id = fields.Many2one("stock.location", string="From Location")
    to_location_id = fields.Many2one("stock.location", string="To Location")
    product_id = fields.Many2one("product.product", string="Product")
    account_id = fields.Many2one("account.account", string="Account")
    consumed_qty = fields.Float(string="Consumed Qty")
    qty_invoiced = fields.Float(string="Invoiced Qty")
    cost_of_goods_account_id = fields.Many2one("account.account", string="Cost of Goods Account")
    damage_line = fields.Boolean(string="Damage Line")
    damaged_consumed_qty = fields.Float(string="Damaged Consumed Qty")
